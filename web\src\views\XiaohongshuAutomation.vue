<template>
  <div class="xiaohongshu-automation">
    <div class="page-header">
      <div class="header-content">
        <div>
          <h2>小红书自动化工具</h2>
          <p>专业的小红书运营自动化工具，支持资料修改、群聊管理、消息群发、文章评论等功能</p>
        </div>
        <div class="header-actions">
          <el-button
            type="info"
            icon="el-icon-refresh"
            size="small"
            @click="refreshVuexState"
            title="刷新状态（解决状态显示不一致问题）"
          >
            刷新状态
          </el-button>

          <el-button
            type="primary"
            icon="el-icon-notebook-1"
            @click="goToLogs"
          >
            查看执行日志
          </el-button>
        </div>
      </div>
    </div>

    <!-- 功能选择卡片 -->
    <el-row :gutter="20" class="function-cards">
      <el-col :span="6" v-for="func in functions" :key="func.key">
        <el-card
          class="function-card"
          :class="{ active: selectedFunction === func.key }"
          @click.native="selectFunction(func.key)"
          shadow="hover"
        >
          <div class="card-content">
            <div class="card-header">
              <i :class="func.icon" class="function-icon"></i>
              <el-button
                v-if="getFunctionRunningCount(func.key) > 0"
                type="danger"
                size="mini"
                icon="el-icon-close"
                class="batch-stop-btn"
                @click.stop="batchStopFunction(func.key)"
                :title="`停止所有${func.name}任务`"
              >
                {{ getFunctionRunningCount(func.key) }}
              </el-button>
            </div>
            <h3>{{ func.name }}</h3>
            <p>{{ func.description }}</p>
            <div v-if="getFunctionRunningCount(func.key) > 0" class="running-status">
              <span class="running-indicator">{{ getFunctionRunningCount(func.key) }}个设备执行中</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- UID文件管理 -->
    <UidFileManager
      v-if="selectedFunction === 'uidFileMessage'"
      :showAllocationConfig="true"
      :selectedDevices="selectedDevices"
      :selectedDeviceCount="selectedDevices.length"
      @allocation-config-change="handleUidAllocationConfigChange"
    />

    <!-- 视频文件管理 -->
    <VideoFileManager
      v-if="selectedFunction === 'videoPublish'"
      @videos-uploaded="handleVideosUploaded"
    />



    <!-- 配置面板 -->
    <el-row :gutter="20" v-if="selectedFunction">
      <!-- 左侧：设备信息 -->
      <el-col :span="8">
        <DeviceInfo
          :enableBatchSelect="true"
          :selectedDeviceIds="selectedDevices"
          :currentFunction="selectedFunction"
          @device-selected="handleDeviceSelected"
          @device-removed="handleDeviceRemoved"
          @devices-selection-changed="handleDevicesSelectionChanged"
        />
      </el-col>

      <!-- 右侧：功能配置 -->
      <el-col :span="16">
        <el-card class="config-panel">
          <div slot="header">
            <span>{{ getCurrentFunction().name }}配置</span>
          </div>

          <!-- 设备选择提示 -->
          <div class="config-section" v-if="selectedDevices.length === 0">
            <el-alert
              title="请先选择执行设备"
              type="warning"
              :closable="false"
              show-icon
            >
              请在左侧设备信息中选择要执行脚本的设备
            </el-alert>
          </div>

          <!-- 已选设备显示 -->
          <div class="config-section" v-else>
            <h4>已选设备 ({{ selectedDevices.length }}个)</h4>
            <el-tag
              v-for="deviceId in selectedDevices"
              :key="deviceId"
              closable
              @close="removeDevice(deviceId)"
              :type="getDeviceTagType(deviceId)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ getDeviceNameWithStatus(deviceId) }}
            </el-tag>
          </div>

          <!-- 多设备配置标签页 -->
          <div class="config-section" v-if="selectedDevices.length > 0">
            <div v-if="!getComponentName()" class="no-component">
              <el-alert
                title="组件加载错误"
                type="warning"
                :closable="false"
                show-icon
              >
                未找到对应的配置组件：{{ selectedFunction }}
              </el-alert>
            </div>

            <div v-else>
              <el-tabs
                v-model="activeDeviceTab"
                type="card"
                @tab-click="handleTabClick"
                class="device-config-tabs"
              >
                <el-tab-pane
                  v-for="deviceId in selectedDevices"
                  :key="deviceId"
                  :label="getDeviceTabLabel(deviceId)"
                  :name="deviceId"
                >
                  <div class="device-config-content">
                    <div class="device-config-header">
                      <h5>{{ getDeviceNameWithStatus(deviceId) }} - 配置参数</h5>
                      <el-button
                        type="primary"
                        size="small"
                        @click="executeForDevice(deviceId)"
                        :loading="executing"
                        :disabled="!isDeviceConfigValid(deviceId)"
                      >
                        执行此设备
                      </el-button>
                    </div>

                    <!-- 功能配置组件 -->
                    <component
                      :is="getComponentName()"
                      v-model="deviceConfigs[deviceId]"
                      @update="handleDeviceConfigUpdate(deviceId, $event)"
                      @validation-error="handleValidationError"
                      @validation-success="handleValidationSuccess"
                      @task-started="handleTaskStarted"
                      @task-stopped="handleTaskStopped"
                      @execute-script="handleExecuteScript"
                      :device-id="deviceId"
                      :selected-devices="selectedFunction === 'videoPublish' ? selectedDevices : [deviceId]"
                      :online-devices="getOnlineDevices()"
                      :key="`${selectedFunction}-${deviceId}`"
                      :ref="`config_${deviceId}`"
                    />
                  </div>
                </el-tab-pane>
              </el-tabs>

              <!-- 批量执行按钮 -->
              <div class="batch-execute-section">
                <el-button
                  type="success"
                  size="medium"
                  @click="executeAllDevices"
                  :loading="executing"
                  :disabled="!hasValidConfigs()"
                  icon="el-icon-s-promotion"
                >
                  批量执行所有设备 ({{ getValidConfigCount() }}/{{ selectedDevices.length }})
                </el-button>
                <el-button
                  type="info"
                  size="medium"
                  @click="copyConfigToAll"
                  :disabled="selectedDevices.length <= 1 || !activeDeviceTab"
                  icon="el-icon-document-copy"
                >
                  复制当前配置到所有设备
                </el-button>

              </div>
            </div>
          </div>

          <!-- 执行计划 -->
          <div class="config-section">
            <h4>执行计划</h4>
            <el-form :model="scheduleConfig" label-width="120px">
              <el-form-item label="执行模式">
                <el-radio-group v-model="scheduleConfig.mode">
                  <el-radio label="immediate">立即执行</el-radio>
                  <el-radio label="scheduled">定时执行</el-radio>
                  <el-radio label="loop">循环执行</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="执行时间" v-if="scheduleConfig.mode === 'scheduled'">
                <el-date-picker
                  v-model="scheduleConfig.scheduledTime"
                  type="datetime"
                  placeholder="选择执行时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>

              <el-form-item label="循环间隔" v-if="scheduleConfig.mode === 'loop'">
                <el-input-number
                  v-model="scheduleConfig.interval"
                  :min="1"
                  :max="1440"
                  placeholder="分钟"
                />
                <span style="margin-left: 10px">分钟</span>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>



    <!-- 执行日志 -->
    <el-card class="log-panel">
      <div slot="header">
        <span>执行日志</span>
        <el-button
          type="info"
          size="small"
          style="float: right"
          @click="clearLogs"
        >
          清空日志
        </el-button>
      </div>

      <div class="log-content">
        <div
          v-for="(log, index) in logs"
          :key="index"
          class="log-item"
          :class="log.level"
        >
          <span class="log-time">{{ $moment(log.time).format('HH:mm:ss') }}</span>
          <span class="log-device">{{ log.device }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="empty-logs">
          暂无执行日志
        </div>
      </div>
    </el-card>

    <!-- 实时状态播报显示 -->
    <div v-if="showRealtimeStatus" class="realtime-status-panel">
    <el-card class="status-card">
      <div slot="header" class="card-header">
        <span>📊 实时执行状态</span>
        <el-button type="text" size="small" @click="hideRealtimeStatus">
          <i class="el-icon-close"></i>
        </el-button>
      </div>

      <div class="status-content">
        <!-- 当前执行的功能 -->
        <div class="status-section">
          <h4>当前执行功能</h4>
          <div class="function-info">
            <span class="function-name">{{ getCurrentFunctionName() }}</span>
            <el-tag :type="getStatusTagType()" size="small">{{ currentExecutionStatus }}</el-tag>
          </div>
        </div>

        <!-- 设备执行状态 -->
        <div class="status-section">
          <h4>设备执行状态</h4>
          <div class="device-status-grid">
            <div
              v-for="device in executingDevices"
              :key="device.deviceId"
              class="device-status-item"
            >
              <div class="device-header">
                <span class="device-name">{{ device.deviceName || device.deviceId }}</span>
                <el-tag :type="getDeviceStatusType(device.status)" size="mini">
                  {{ device.status }}
                </el-tag>
              </div>

              <!-- 实时状态信息 -->
              <div v-if="device.realtimeStatus" class="realtime-info">
                <div class="status-row">
                  <span class="label">当前步骤：</span>
                  <span class="value">{{ device.realtimeStatus.currentStep }}</span>
                </div>
                <div class="status-row">
                  <span class="label">执行状态：</span>
                  <span class="value" :class="getStatusClass(device.realtimeStatus.currentStatus)">
                    {{ device.realtimeStatus.currentStatus }}
                  </span>
                </div>
                <div v-if="device.realtimeStatus.message" class="status-row">
                  <span class="label">状态信息：</span>
                  <span class="value">{{ device.realtimeStatus.message }}</span>
                </div>
                <div v-if="device.realtimeStatus.errorMessage" class="status-row error">
                  <span class="label">错误信息：</span>
                  <span class="value">{{ device.realtimeStatus.errorMessage }}</span>
                </div>

                <!-- 进度信息（如果有的话） -->
                <div v-if="hasProgressInfo(device.realtimeStatus)" class="progress-info">
                  <div class="progress-text">
                    进度: {{ device.realtimeStatus.processedCount || 0 }} / {{ device.realtimeStatus.totalCount || 0 }}
                  </div>
                  <el-progress
                    :percentage="getDeviceProgress(device.realtimeStatus)"
                    :status="getProgressStatus(device.realtimeStatus)"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    </div>
  </div>
</template>

<script>
// 导入配置组件
import ProfileConfig from '@/components/xiaohongshu/ProfileConfig.vue'
import SearchGroupChatConfig from '@/components/xiaohongshu/SearchGroupChatConfig.vue'
import GroupMessageConfigOriginal from '@/components/xiaohongshu/GroupMessageConfigOriginal.vue'
import ArticleCommentConfig from '@/components/xiaohongshu/ArticleCommentConfig.vue'
import UidMessageConfig from '@/components/xiaohongshu/UidMessageConfig.vue'
import UidFileMessageConfig from '@/components/xiaohongshu/UidFileMessageConfig.vue'
import UidFileManager from '@/components/xiaohongshu/UidFileManager.vue'
import VideoFileManager from '@/components/xiaohongshu/VideoFileManager.vue'
import VideoPublishConfig from '@/components/xiaohongshu/VideoPublishConfig.vue'
import DeviceInfo from '@/components/xiaohongshu/DeviceInfo.vue'

export default {
  name: 'XiaohongshuAutomation',
  components: {
    ProfileConfig,
    SearchGroupChatConfig,
    GroupMessageConfigOriginal,
    ArticleCommentConfig,
    UidMessageConfig,
    UidFileMessageConfig,
    UidFileManager,
    VideoFileManager,
    VideoPublishConfig,
    DeviceInfo
  },
  data() {
    return {
      selectedFunction: '',
      selectedDevices: [],
      executing: false,
      functionConfig: {}, // 保留作为默认配置模板

      // 实时状态播报相关
      showRealtimeStatus: false,
      currentExecutionStatus: '等待开始',
      executingDevices: [], // 正在执行的设备列表
      realtimeStatusData: {}, // 设备实时状态数据
      deviceConfigs: {}, // 新增：每个设备的独立配置 { deviceId: config }
      scheduleConfig: {
        mode: 'immediate',
        scheduledTime: '',
        interval: 30
      },
      logs: [],
      uidAllocationConfig: null, // UID分配配置
      functions: [
        {
          key: 'profile',
          name: '修改资料',
          description: '修改小红书昵称和个人简介',
          icon: 'el-icon-user'
        },
        {
          key: 'searchGroupChat',
          name: '搜索加群',
          description: '搜索关键词群聊并自动加群发消息',
          icon: 'el-icon-chat-dot-round'
        },
        {
          key: 'groupMessage',
          name: '循环群发',
          description: '循环群发消息到所有群聊',
          icon: 'el-icon-message'
        },
        {
          key: 'articleComment',
          name: '文章评论',
          description: '搜索关键词文章并自动评论',
          icon: 'el-icon-chat-line-round'
        },
        {
          key: 'uidMessage',
          name: '手动输入UID私信',
          description: '手动输入UID列表并发送私信',
          icon: 'el-icon-edit'
        },
        {
          key: 'uidFileMessage',
          name: '文件上传UID私信',
          description: '上传UID文件并批量发送私信',
          icon: 'el-icon-upload2'
        },
        {
          key: 'videoPublish',
          name: '发布视频',
          description: '批量上传和发布视频到小红书',
          icon: 'el-icon-video-camera'
        }
      ],
      // 性能优化：防抖定时器
      loadDevicesTimer: null,
      // 新增：当前活跃的设备配置标签
      activeDeviceTab: ''
    }
  },
  computed: {
    onlineDevices() {
      return this.$store.getters['device/onlineDevices']
    },
    showUidFileManager() {
      // 当选择UID私信功能时显示文件管理器
      return this.selectedFunction === 'uidMessage'
    }
  },
  async mounted() {
    console.log('🔧 [XiaohongshuAutomation] 组件挂载开始')

    // 恢复WebSocket连接活动
    const websocketManager = this.$store.getters['socket/websocketManager']
    if (websocketManager && typeof websocketManager.resumeConnection === 'function') {
      console.log('🔧 [XiaohongshuAutomation] 恢复WebSocket连接活动')
      websocketManager.resumeConnection()
    }

    await this.loadDevices()

    // 检查是否是重试执行
    this.handleRetryExecution()

    // 恢复页面状态
    await this.restorePageState()

    // 检查是否有正在运行的任务，恢复状态
    await this.checkAndRestoreRunningTasks()

    // 使用全局WebSocket连接，不重复初始化
    await this.setupWebSocketListeners()

    // 初始化全局事件监听
    this.initGlobalEventListeners()

    // 添加调试消息监听器
    this.initDebugMessageListener()

    console.log('🔧 [XiaohongshuAutomation] 组件挂载完成')
  },
  beforeDestroy() {
    console.log('🔧 [XiaohongshuAutomation] 组件即将销毁，执行清理操作')

    // 保存页面状态
    this.savePageState()

    // 清理Socket事件监听（但不断开连接）
    this.cleanupSocketListeners()

    // 清理全局事件监听
    this.cleanupGlobalEventListeners()

    // 清理定时器，防止内存泄漏
    if (this.loadDevicesTimer) {
      clearTimeout(this.loadDevicesTimer)
      this.loadDevicesTimer = null
    }

    // 使用软断开而不是完全断开WebSocket连接
    const websocketManager = this.$store.getters['socket/websocketManager']
    if (websocketManager && typeof websocketManager.softDisconnect === 'function') {
      console.log('🔧 [XiaohongshuAutomation] 执行WebSocket软断开')
      websocketManager.softDisconnect()
    } else {
      console.log('🔧 [XiaohongshuAutomation] WebSocket管理器不可用或不支持软断开')
    }

    console.log('🔧 [XiaohongshuAutomation] 组件清理完成')
  },
  methods: {
    async loadDevices() {
      await this.$store.dispatch('device/fetchDevices')
    },

    // 处理UID分配配置变化
    handleUidAllocationConfigChange(config) {
      console.log('UID分配配置变化:', config)
      this.uidAllocationConfig = config

      // 更新所有设备的配置
      this.selectedDevices.forEach(deviceId => {
        if (this.deviceConfigs[deviceId]) {
          this.deviceConfigs[deviceId] = {
            ...this.deviceConfigs[deviceId],
            selectedFileId: config.selectedFileId,
            totalUidCount: config.totalUidCount,
            uidsPerDevice: config.uidsPerDevice
          }
        }
      })
    },

    // 处理视频上传完成
    handleVideosUploaded(data) {
      console.log('视频上传完成:', data)
      let message = `成功上传 ${data.uploadCount} 个视频文件`
      if (data.duplicateCount > 0) {
        message += `，跳过 ${data.duplicateCount} 个重复文件`
      }
      this.$message.success(message)
    },

    // 处理重试执行
    handleRetryExecution() {
      const query = this.$route.query

      if (query.retry === 'true') {
        console.log('检测到重试执行请求:', query)

        // 设置功能类型
        if (query.functionType) {
          this.selectedFunction = query.functionType
          console.log('已设置功能类型:', this.selectedFunction)
        }

        // 设置设备选择
        if (query.deviceId) {
          this.selectedDevices = [query.deviceId]
          console.log('已设置设备选择:', this.selectedDevices)
        }

        // 设置配置参数
        if (query.config) {
          try {
            this.functionConfig = JSON.parse(query.config)
            console.log('已恢复配置参数:', this.functionConfig)
          } catch (e) {
            console.warn('解析配置参数失败:', e)
          }
        }

        // 显示提示信息
        this.$message.info(`已从执行日志恢复"${this.getFunctionTypeName(query.functionType)}"功能的配置`)

        // 清理URL参数，避免刷新时重复处理
        this.$router.replace({
          name: 'XiaohongshuAutomation',
          query: {}
        })
      }
    },

    // 获取功能类型名称
    getFunctionTypeName(functionType) {
      const functionMap = {
        'profile': '修改资料',
        'searchGroupChat': '搜索加群',
        'groupMessage': '循环群发',
        'articleComment': '文章评论',
        'uidMessage': '手动输入UID私信',
        'uidFileMessage': '文件上传UID私信',
        'videoPublish': '发布视频'
      }
      return functionMap[functionType] || functionType
    },

    // 获取指定功能正在运行的设备数量
    getFunctionRunningCount(functionType) {
      try {
        // 方法1：通过Vuex状态统计
        const functionState = this.$store.getters['xiaohongshu/getFunctionState'](functionType)
        if (functionState && functionState.isScriptRunning && functionState.selectedDevices) {
          console.log(`[${functionType}] Vuex状态显示正在运行，设备数量:`, functionState.selectedDevices.length)
          return functionState.selectedDevices.length
        }

        // 方法2：通过设备状态统计（备用方法）
        const onlineDevices = this.$store.getters['device/onlineDevices'] || []
        const busyDevices = this.$store.getters['device/busyDevices'] || []
        const allDevices = [...onlineDevices, ...busyDevices]

        // 统计正在执行该功能的设备数量
        let runningCount = 0

        // 检查每个设备是否正在执行该功能的任务
        allDevices.forEach(device => {
          const deviceTasks = this.$store.getters['xiaohongshu/getDeviceTasks'](device.device_id) || []
          const hasRunningTask = deviceTasks.some(task =>
            task.functionType === functionType &&
            (task.status === 'running' || device.status === 'busy')
          )
          if (hasRunningTask) {
            runningCount++
          }
        })

        console.log(`[${functionType}] 通过设备状态统计的运行数量:`, runningCount)
        return runningCount
      } catch (error) {
        console.warn(`获取${functionType}运行状态失败:`, error)
        return 0
      }
    },

    // 批量停止指定功能的所有任务
    async batchStopFunction(functionType) {
      try {
        const functionName = this.getFunctionTypeName(functionType)
        const runningCount = this.getFunctionRunningCount(functionType)

        if (runningCount === 0) {
          this.$message.warning(`当前没有正在执行的${functionName}任务`)
          return
        }

        // 确认对话框
        const confirmResult = await this.$confirm(
          `确定要停止所有正在执行的${functionName}任务吗？\n当前有 ${runningCount} 个设备正在执行此功能。`,
          '批量停止确认',
          {
            confirmButtonText: '确定停止',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        ).catch(() => false)

        if (!confirmResult) {
          return
        }

        console.log(`开始批量停止${functionType}功能的所有任务`)

        // 发送批量停止请求
        const response = await this.$http.post('/api/xiaohongshu/stop-by-function', {
          functionType: functionType
        })

        if (response.data.success) {
          const { stoppedDevices, updatedTasks } = response.data.data
          this.$message.success(
            `批量停止成功！已向 ${stoppedDevices} 个设备发送停止命令，更新了 ${updatedTasks} 个执行记录`
          )

          console.log(`批量停止${functionType}成功:`, response.data.data)
        } else {
          this.$message.error(`批量停止失败: ${response.data.message}`)
        }

      } catch (error) {
        console.error(`批量停止${functionType}失败:`, error)
        this.$message.error(`批量停止失败: ${error.message}`)
      }
    },

    // 防抖加载设备列表（性能优化）
    debouncedLoadDevices() {
      if (this.loadDevicesTimer) {
        clearTimeout(this.loadDevicesTimer)
      }
      this.loadDevicesTimer = setTimeout(() => {
        this.loadDevices()
      }, 300) // 300ms防抖
    },

    // 设置WebSocket事件监听（使用全局连接）
    async setupWebSocketListeners() {
      try {
        console.log('🔧 [XiaohongshuAutomation] 设置WebSocket事件监听...')

        // 获取全局WebSocket管理器（不重新初始化）
        const { getWebSocketManager } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        // 等待连接就绪（如果还没连接的话）
        if (!this.wsManager.isConnected) {
          console.log('🔧 [XiaohongshuAutomation] 等待WebSocket连接就绪...')
          await new Promise((resolve) => {
            const checkConnection = () => {
              if (this.wsManager.isConnected) {
                resolve()
              } else {
                setTimeout(checkConnection, 100)
              }
            }
            checkConnection()
          })
        }

        // 初始化Socket事件监听
        this.initSocketListeners()

        // 检查连接状态
        this.checkSocketConnection()

        console.log('✅ [XiaohongshuAutomation] WebSocket事件监听设置完成')
      } catch (error) {
        console.error('❌ [XiaohongshuAutomation] WebSocket事件监听设置失败:', error)
        this.$message.error('WebSocket事件监听设置失败，请刷新页面重试')
      }
    },

    // 检查Socket连接状态
    checkSocketConnection() {
      const socket = this.$store.getters['socket/socket']
      console.log('🔧 [XiaohongshuAutomation] 检查Socket连接状态')
      console.log('Socket对象:', socket)
      console.log('Socket连接状态:', socket ? socket.connected : '无Socket对象')
      console.log('Socket ID:', socket ? socket.id : '无Socket ID')

      if (socket && socket.connected) {
        console.log('✅ [XiaohongshuAutomation] Socket连接正常')
        console.log('Socket事件监听器数量:', Object.keys(socket._callbacks || {}).length)

        // 测试发送一个事件
        socket.emit('test_connection', {
          clientType: 'xiaohongshu_automation',
          timestamp: new Date().toISOString()
        })
        console.log('📡 [XiaohongshuAutomation] 已发送测试连接事件')
      } else {
        console.warn('⚠️ [XiaohongshuAutomation] Socket对象不存在或未连接，无法建立连接')
      }
    },

    // 初始化全局事件监听
    initGlobalEventListeners() {
      console.log('小红书自动化页面: 初始化全局事件监听')

      // 监听停止脚本事件
      this.$root.$on('xiaohongshu-stop-script', this.handleStopScript)

      // 监听视频分配同步事件
      this.$root.$on('xiaohongshu-video-assignment-sync', this.handleVideoAssignmentSync)
    },

    // 处理停止脚本事件
    handleStopScript(data) {
      console.log('小红书自动化页面: 收到停止脚本事件', data)

      const { functionType, deviceId, logId, taskId } = data

      // 通过Socket发送停止命令到服务器
      const socket = this.$store.getters['socket/socket']
      if (socket) {
        socket.emit('xiaohongshu_stop_script', {
          functionType,
          deviceId,
          logId,
          taskId
        })
        console.log('已通过Socket发送停止脚本命令')
      } else {
        console.error('Socket未连接，无法发送停止脚本命令')
        this.$message.error('网络连接异常，无法停止脚本')
      }
    },

    // 处理视频分配同步事件
    async handleVideoAssignmentSync(assignmentData) {
      try {
        console.log('🎬 收到视频分配同步事件:', assignmentData)

        // 1. 切换到视频发布功能
        this.selectedFunction = 'videoPublish'
        console.log('✅ 已切换到视频发布功能')

        // 2. 选中分配的设备
        this.selectedDevices = [...assignmentData.selectedDevices]
        console.log('✅ 已选中分配的设备:', this.selectedDevices)

        // 3. 等待组件渲染完成
        await this.$nextTick()

        // 4. 为每个设备配置对应的视频
        const deviceVideoAssignments = assignmentData.deviceVideoAssignments

        // 先清除所有设备的视频选择
        console.log('🗑️ 开始清除所有设备的视频选择')
        for (const deviceId of this.selectedDevices) {
          this.$root.$emit('xiaohongshu-video-publish-clear-video', {
            deviceId: deviceId
          })
        }

        // 等待清除完成后再分配新视频
        setTimeout(() => {
          for (const deviceId of this.selectedDevices) {
            if (deviceVideoAssignments[deviceId]) {
              const assignment = deviceVideoAssignments[deviceId]
              console.log(`🎯 为设备 ${deviceId} 配置视频:`, assignment.assignedVideo)

              // 确保设备配置存在
              if (!this.deviceConfigs[deviceId]) {
                this.deviceConfigs[deviceId] = {
                  title: '',
                  description: '',
                  hashtags: '',
                  publishOptions: ['allowComment', 'allowShare'],
                  operationDelay: 5,
                  retryCount: 2
                }
              }

              // 通过全局事件通知对应的VideoPublishConfig组件选择视频
              this.$root.$emit('xiaohongshu-video-publish-select-video', {
                deviceId: deviceId,
                video: assignment.assignedVideo,
                videoInfo: assignment.videoInfo
              })
            }
          }
        }, 100) // 延迟100ms确保清除完成

        // 5. 保存页面状态
        this.savePageState()

        // 6. 显示成功消息
        this.$message.success(`视频分配同步完成！已为 ${this.selectedDevices.length} 个设备配置对应视频`)

        console.log('✅ 视频分配同步处理完成')

      } catch (error) {
        console.error('❌ 处理视频分配同步失败:', error)
        this.$message.error('同步视频分配失败: ' + error.message)
      }
    },

    // 初始化Socket事件监听
    initSocketListeners() {
      const socket = this.$store.getters['socket/socket']
      console.log('小红书自动化页面: 检查Socket连接状态', socket ? '已连接' : '未连接')

      if (socket) {
        // 监听设备状态变化事件（包括离线）
        socket.on('device_status_changed', this.handleDeviceStatusChanged)
        // 监听设备离线事件（兼容旧版本）
        socket.on('device_offline', this.handleDeviceOffline)
        // 监听设备状态更新
        socket.on('device_status_update', this.handleDeviceStatusUpdate)
        // 监听所有任务停止事件
        socket.on('xiaohongshu_all_tasks_stopped', this.handleAllTasksStopped)
        // 监听按功能类型批量停止事件
        socket.on('xiaohongshu_function_tasks_stopped', this.handleFunctionTasksStopped)
        // 监听任务更新事件
        socket.on('xiaohongshu_task_update', this.handleTaskUpdate)
        // 监听脚本状态更新事件
        socket.on('xiaohongshu_status_update', this.handleScriptStatusUpdate)
        // 监听调试日志事件
        socket.on('xiaohongshu_debug_log', this.handleDebugLog)
        // 监听脚本执行完成事件
        socket.on('xiaohongshu_execution_completed', (data) => {
          console.log('🎯 [XiaohongshuAutomation] Socket收到脚本执行完成事件:', data)
          this.handleExecutionCompleted(data)
        })
        // 监听脚本停止完成事件
        socket.on('xiaohongshu_script_completed', this.handleScriptCompleted)
        // 监听任务开始事件（从后端发送）
        socket.on('xiaohongshu_task_started', this.handleTaskStartedFromBackend)
        // 监听实时状态更新事件（统一处理）
        socket.on('xiaohongshu_realtime_status', this.handleMainRealtimeStatus)
        // 监听服务器准备关闭事件
        socket.on('server_prepare_shutdown', (data) => {
          console.log('📢 [XiaohongshuAutomation] Socket收到服务器准备关闭事件:', data)
          this.handleServerPrepareShutdown(data)
        })
        // 监听服务器关闭事件
        socket.on('server_shutdown', (data) => {
          console.log('📢 [XiaohongshuAutomation] Socket收到服务器关闭事件:', data)
          this.handleServerShutdown(data)
        })
        // 监听设备脚本状态事件
        socket.on('device_script_status', (data) => {
          console.log('🛑 [XiaohongshuAutomation] Socket收到设备脚本状态事件:', data)
          this.handleDeviceScriptStatus(data)
        })
        // 监听设备应用状态事件
        socket.on('device_app_status', (data) => {
          console.log('📱 [XiaohongshuAutomation] Socket收到设备应用状态事件:', data)
          this.handleDeviceAppStatus(data)
        })
        // 监听Socket连接断开事件
        socket.on('disconnect', this.handleSocketDisconnect)

        // 🔥 新增：监听脚本重置事件
        socket.on('xiaohongshu_script_reset', this.handleScriptReset)

        // 🔥 新增：监听Vuex状态更新事件
        socket.on('xiaohongshu_vuex_state_update', this.handleVuexStateUpdate)
        socket.on('xiaohongshu_force_refresh_vuex', this.handleForceRefreshVuex)
        // 🔥 新增：监听功能状态清理事件
        socket.on('xiaohongshu_clear_function_state', this.handleClearFunctionState)

        // 添加测试事件监听
        socket.on('test_event', (data) => {
          console.log('🧪 收到测试事件:', data)
        })

        console.log('✅ [XiaohongshuAutomation] Socket事件监听已初始化')
        console.log('📋 [XiaohongshuAutomation] 监听的事件包括:', [
          'device_offline',
          'device_status_update',
          'xiaohongshu_all_tasks_stopped',
          'xiaohongshu_task_update',
          'xiaohongshu_status_update',
          'xiaohongshu_debug_log',
          'xiaohongshu_execution_completed',
          'xiaohongshu_script_reset',
          'xiaohongshu_vuex_state_update',
          'xiaohongshu_force_refresh_vuex',
          'test_event'
        ])

        // 不需要重复注册客户端，WebSocketManager已经注册了全局客户端
        console.log('📡 [XiaohongshuAutomation] 使用WebSocketManager的全局客户端连接')

        // 测试Socket连接
        setTimeout(() => {
          console.log('🧪 [XiaohongshuAutomation] 发送测试事件到服务器')
          socket.emit('client_test', {
            message: '来自小红书自动化页面的测试消息',
            timestamp: new Date().toISOString()
          })
        }, 1000) // 减少延迟时间
      } else {
        console.warn('⚠️ [XiaohongshuAutomation] Socket未连接，无法初始化事件监听')
      }
    },

    // 清理Socket事件监听
    cleanupSocketListeners() {
      const socket = this.$store.getters['socket/socket']
      if (socket) {
        socket.off('device_status_changed', this.handleDeviceStatusChanged)
        socket.off('device_offline', this.handleDeviceOffline)
        socket.off('device_status_update', this.handleDeviceStatusUpdate)
        socket.off('xiaohongshu_all_tasks_stopped', this.handleAllTasksStopped)
        socket.off('xiaohongshu_function_tasks_stopped', this.handleFunctionTasksStopped)
        socket.off('xiaohongshu_task_update', this.handleTaskUpdate)
        socket.off('xiaohongshu_status_update', this.handleScriptStatusUpdate)
        socket.off('xiaohongshu_debug_log', this.handleDebugLog)
        socket.off('xiaohongshu_execution_completed')
        socket.off('xiaohongshu_script_completed', this.handleScriptCompleted)
        socket.off('xiaohongshu_realtime_status', this.handleMainRealtimeStatus)
        socket.off('server_prepare_shutdown')
        socket.off('server_shutdown')
        socket.off('device_script_status')
        socket.off('device_app_status')
        socket.off('disconnect', this.handleSocketDisconnect)
        console.log('小红书自动化页面: Socket事件监听已清理')
      }
    },

    // 清理全局事件监听
    cleanupGlobalEventListeners() {
      console.log('小红书自动化页面: 清理全局事件监听')
      this.$root.$off('xiaohongshu-stop-script', this.handleStopScript)
      this.$root.$off('xiaohongshu-video-assignment-sync', this.handleVideoAssignmentSync)
    },

    // 处理设备状态变化事件（新版本）
    handleDeviceStatusChanged(data) {
      console.log('🟡 [XiaohongshuAutomation] 收到设备状态变化事件:', data)
      const { type, deviceId, status } = data

      // 更新store中的设备状态
      if (deviceId && status) {
        this.$store.dispatch('device/updateDeviceStatus', {
          deviceId,
          status,
          ...data
        })
      }

      if (type === 'device_disconnected') {
        console.log('🟡 [XiaohongshuAutomation] 设备断开连接，调用handleDeviceDisconnected')
        this.handleDeviceDisconnected(deviceId)
      }
    },

    // 处理设备离线事件（兼容旧版本）
    async handleDeviceOffline(data) {
      console.log('🔴 [XiaohongshuAutomation] 收到设备离线事件:', data)
      const { deviceId } = data
      console.log('🔴 [XiaohongshuAutomation] 处理设备离线，deviceId:', deviceId)
      console.log('🔴 [XiaohongshuAutomation] 当前选中设备:', this.selectedDevices)
      console.log('🔴 [XiaohongshuAutomation] 当前设备配置:', Object.keys(this.deviceConfigs))

      // 首先停止该设备的所有脚本和更新执行状态
      await this.stopDeviceScriptsAndUpdateStatus(deviceId)

      // 然后处理设备断开连接
      this.handleDeviceDisconnected(deviceId)
    },

    // 统一处理设备断开连接
    handleDeviceDisconnected(deviceId) {
      console.log(`🔴 [XiaohongshuAutomation] 处理设备断开连接: ${deviceId}`)
      console.log(`🔴 [XiaohongshuAutomation] 当前选中设备列表:`, this.selectedDevices)
      console.log(`🔴 [XiaohongshuAutomation] 当前设备配置键:`, Object.keys(this.deviceConfigs))

      // 尝试多种设备ID格式匹配
      let targetDeviceId = deviceId
      let selectedIndex = this.selectedDevices.indexOf(deviceId)

      // 如果直接匹配失败，尝试其他可能的格式
      if (selectedIndex === -1) {
        console.log(`🔴 [XiaohongshuAutomation] 直接匹配失败，尝试其他格式`)

        // 尝试查找所有设备，看看是否有匹配的
        const allDevices = this.$store.getters['device/devices'] || []
        console.log(`🔴 [XiaohongshuAutomation] 所有设备:`, allDevices.map(d => ({ id: d.device_id, name: d.device_name })))

        // 尝试通过设备名称或IP地址匹配
        const matchingDevice = allDevices.find(device => {
          return device.device_id === deviceId ||
                 device.device_name === deviceId ||
                 (device.ip_address && deviceId.includes(device.ip_address.replace(/\./g, '_')))
        })

        if (matchingDevice) {
          targetDeviceId = matchingDevice.device_id
          selectedIndex = this.selectedDevices.indexOf(targetDeviceId)
          console.log(`🔴 [XiaohongshuAutomation] 通过匹配找到设备: ${targetDeviceId}`)
        }
      }

      console.log(`🔴 [XiaohongshuAutomation] 最终使用的设备ID: ${targetDeviceId}`)
      console.log(`🔴 [XiaohongshuAutomation] 设备在选中列表中的索引:`, selectedIndex)

      if (selectedIndex !== -1) {
        this.selectedDevices.splice(selectedIndex, 1)
        console.log(`🔴 [XiaohongshuAutomation] 已从选中列表移除设备，新列表:`, this.selectedDevices)
        this.addLog('系统', `设备 ${targetDeviceId} 已断开连接，已从选中列表移除`, 'warning')
      } else {
        console.log(`🔴 [XiaohongshuAutomation] 设备 ${deviceId} 不在选中列表中`)
        console.log(`🔴 [XiaohongshuAutomation] 选中设备详情:`, this.selectedDevices)
      }

      // 清理该设备的配置参数
      console.log(`🔴 [XiaohongshuAutomation] 检查设备配置，deviceId: ${targetDeviceId}`)
      console.log(`🔴 [XiaohongshuAutomation] 设备配置存在:`, !!this.deviceConfigs[targetDeviceId])
      if (this.deviceConfigs[targetDeviceId]) {
        this.$delete(this.deviceConfigs, targetDeviceId)
        console.log(`🔴 [XiaohongshuAutomation] 已清理设备 ${targetDeviceId} 的配置参数`)
        console.log(`🔴 [XiaohongshuAutomation] 清理后的配置键:`, Object.keys(this.deviceConfigs))
        this.addLog('系统', `已清理设备 ${targetDeviceId} 的配置参数`, 'info')
      } else {
        console.log(`🔴 [XiaohongshuAutomation] 设备 ${targetDeviceId} 没有配置参数需要清理`)
      }

      // 如果移除的是当前活跃标签，切换到第一个设备
      if (this.activeDeviceTab === targetDeviceId) {
        if (this.selectedDevices.length > 0) {
          this.activeDeviceTab = this.selectedDevices[0]
          console.log(`活跃标签页已切换到: ${this.activeDeviceTab}`)
        } else {
          this.activeDeviceTab = ''
          console.log('已清空活跃标签页')
        }
      }

      // 清理该设备的执行状态
      console.log(`🔴 [XiaohongshuAutomation] 开始清理设备执行状态: ${targetDeviceId}`)
      this.clearDeviceExecutionState(targetDeviceId)

      // 额外清理：确保Vuex中该设备的所有功能状态都被清除
      console.log(`🔴 [XiaohongshuAutomation] 强制清理Vuex中设备的功能状态`)
      this.forceCleanDeviceFromVuex(targetDeviceId)

      // 通知所有配置组件设备离线
      this.$root.$emit('device-offline', {
        deviceId: targetDeviceId
      })

      // 强制更新Vue响应式数据
      this.$forceUpdate()

      // 防抖刷新设备列表（避免频繁刷新）
      this.debouncedLoadDevices()

      console.log(`🔴 [XiaohongshuAutomation] 设备离线处理完成，当前状态:`)
      console.log(`🔴 [XiaohongshuAutomation] - 选中设备:`, this.selectedDevices)
      console.log(`🔴 [XiaohongshuAutomation] - 设备配置:`, Object.keys(this.deviceConfigs))
      console.log(`🔴 [XiaohongshuAutomation] - 活跃标签:`, this.activeDeviceTab)
    },

    // 处理服务器关闭事件
    handleServerShutdown(data) {
      console.log('🔴 [XiaohongshuAutomation] 收到服务器关闭事件:', data)

      // 显示服务器关闭提示
      this.$message.error('服务器即将关闭')
      this.addLog('系统', '服务器即将关闭', 'error')

      console.log('🔴 [XiaohongshuAutomation] 服务器关闭事件处理完成')
    },

    // 处理设备脚本状态事件
    handleDeviceScriptStatus(data) {
      console.log('🛑 [XiaohongshuAutomation] 收到设备脚本状态:', data)

      if (data.status === 'script_stopped') {
        console.log(`🛑 [XiaohongshuAutomation] 设备 ${data.deviceId} 脚本已停止`)
        this.addLog('系统', `设备 ${data.deviceId} 脚本已停止: ${data.reason}`, 'warning')
        this.$message.warning(`设备脚本已停止: ${data.reason}`)
      }
    },

    // 处理设备应用状态事件
    handleDeviceAppStatus(data) {
      console.log('📱 [XiaohongshuAutomation] 收到设备应用状态:', data)

      if (data.status === 'app_closed') {
        console.log(`📱 [XiaohongshuAutomation] 设备 ${data.deviceId} 小红书应用已关闭`)
        this.addLog('系统', `设备 ${data.deviceId} 小红书应用已关闭: ${data.reason}`, 'info')
        this.$message.info(`设备应用已关闭: ${data.reason}`)
      }
    },

    // 处理服务器准备关闭事件
    handleServerPrepareShutdown(data) {
      console.log('🔄 [XiaohongshuAutomation] 收到服务器准备关闭事件:', data)

      // 立即重置所有前端状态
      console.log('🔄 [XiaohongshuAutomation] 开始重置前端状态')

      // 获取当前选中的设备列表副本
      const devicesToClean = [...this.selectedDevices]
      console.log('🔄 [XiaohongshuAutomation] 需要清理的设备:', devicesToClean)

      // 清理所有选中的设备
      devicesToClean.forEach(deviceId => {
        console.log(`🔄 [XiaohongshuAutomation] 清理设备: ${deviceId}`)
        this.handleDeviceDisconnected(deviceId)
      })

      // 强制清空所有状态
      this.selectedDevices = []
      this.deviceConfigs = {}
      this.activeDeviceTab = ''

      // 重置全局执行状态
      this.$store.dispatch('xiaohongshu/resetGlobalExecutionState')

      // 通知所有配置组件重置
      this.$root.$emit('force-reset-all', {
        reason: 'server_prepare_shutdown',
        timestamp: new Date().toISOString()
      })

      // 强制更新Vue响应式数据
      this.$forceUpdate()

      // 显示准备关闭提示
      this.$message.warning('服务器准备关闭，正在重置状态...')
      this.addLog('系统', '服务器准备关闭，已重置所有状态', 'warning')

      console.log('🔄 [XiaohongshuAutomation] 前端状态重置完成')
      console.log('🔄 [XiaohongshuAutomation] 重置后状态 - 选中设备:', this.selectedDevices)
      console.log('🔄 [XiaohongshuAutomation] 重置后状态 - 设备配置:', Object.keys(this.deviceConfigs))
    },

    // 处理Socket连接断开事件
    async handleSocketDisconnect(reason) {
      console.log('🔌 [XiaohongshuAutomation] Socket连接断开:', reason)
      console.log('🔌 [XiaohongshuAutomation] 当前选中设备:', this.selectedDevices)
      console.log('🔌 [XiaohongshuAutomation] 当前设备配置:', Object.keys(this.deviceConfigs))
      console.log('🔌 [XiaohongshuAutomation] 当前执行状态:', this.executing)

      // 无论什么原因断开，都清理所有设备状态（因为服务器不可用）
      console.log('🔌 [XiaohongshuAutomation] 服务器不可用，清理所有设备状态')

      // 获取当前选中的设备列表副本
      const devicesToClean = [...this.selectedDevices]
      console.log('🔌 [XiaohongshuAutomation] 需要清理的设备:', devicesToClean)

      // 停止所有设备的脚本并更新状态
      for (const deviceId of devicesToClean) {
        console.log(`🔌 [XiaohongshuAutomation] 停止设备脚本: ${deviceId}`)
        await this.stopDeviceScriptsAndUpdateStatus(deviceId)
      }

      // 清理所有选中的设备
      devicesToClean.forEach(deviceId => {
        console.log(`🔌 [XiaohongshuAutomation] 清理设备: ${deviceId}`)
        this.handleDeviceDisconnected(deviceId)
      })

      // 强制清空所有状态
      console.log('🔌 [XiaohongshuAutomation] 强制清空所有状态')
      this.selectedDevices = []
      this.deviceConfigs = {}
      this.activeDeviceTab = ''

      // 重置全局执行状态
      this.$store.dispatch('xiaohongshu/resetGlobalExecutionState')

      // 通知所有配置组件服务器断开
      this.$root.$emit('server-disconnect', {
        reason: reason,
        timestamp: new Date().toISOString()
      })

      // 显示提示信息
      this.$message.warning('服务器连接已断开，所有设备状态已清理')
      this.addLog('系统', `服务器连接断开 (${reason})，已清理所有设备状态`, 'warning')

      console.log('🔌 [XiaohongshuAutomation] Socket断开处理完成')
      console.log('🔌 [XiaohongshuAutomation] 清理后状态 - 选中设备:', this.selectedDevices)
      console.log('🔌 [XiaohongshuAutomation] 清理后状态 - 设备配置:', Object.keys(this.deviceConfigs))
    },

    // 强制重置所有状态
    async forceResetAllStates() {
      console.log('🔄 [XiaohongshuAutomation] 用户触发强制重置所有状态')

      this.$confirm('确定要强制重置所有设备状态和配置吗？这将停止所有脚本并清除所有选中的设备和配置参数。', '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        console.log('🔄 [XiaohongshuAutomation] 开始强制重置')

        // 获取当前选中的设备列表副本
        const devicesToClean = [...this.selectedDevices]
        console.log('🔄 [XiaohongshuAutomation] 需要清理的设备:', devicesToClean)

        // 停止所有设备的脚本并更新状态
        for (const deviceId of devicesToClean) {
          console.log(`🔄 [XiaohongshuAutomation] 停止设备脚本: ${deviceId}`)
          await this.stopDeviceScriptsAndUpdateStatus(deviceId)
        }

        // 清理所有选中的设备
        devicesToClean.forEach(deviceId => {
          console.log(`🔄 [XiaohongshuAutomation] 清理设备: ${deviceId}`)
          this.handleDeviceDisconnected(deviceId)
        })

        // 强制清空所有状态
        this.selectedDevices = []
        this.deviceConfigs = {}
        this.activeDeviceTab = ''

        // 重置全局执行状态
        this.$store.dispatch('xiaohongshu/resetGlobalExecutionState')

        // 通知所有配置组件重置
        this.$root.$emit('force-reset-all', {
          timestamp: new Date().toISOString()
        })

        // 强制更新Vue响应式数据
        this.$forceUpdate()

        // 显示成功提示
        this.$message.success('所有状态已强制重置')
        this.addLog('系统', '用户手动强制重置所有状态', 'info')

        console.log('🔄 [XiaohongshuAutomation] 强制重置完成')
        console.log('🔄 [XiaohongshuAutomation] 重置后状态 - 选中设备:', this.selectedDevices)
        console.log('🔄 [XiaohongshuAutomation] 重置后状态 - 设备配置:', Object.keys(this.deviceConfigs))
      }).catch(() => {
        console.log('🔄 [XiaohongshuAutomation] 用户取消强制重置')
      })
    },



    // 停止设备脚本并更新执行状态
    async stopDeviceScriptsAndUpdateStatus(deviceId) {
      console.log(`🛑 [XiaohongshuAutomation] 停止设备脚本并更新状态: ${deviceId}`)
      console.log(`🛑 [XiaohongshuAutomation] 原始设备ID: "${deviceId}"`)
      console.log(`🛑 [XiaohongshuAutomation] 设备ID类型: ${typeof deviceId}`)
      console.log(`🛑 [XiaohongshuAutomation] 设备ID长度: ${deviceId ? deviceId.length : 'undefined'}`)

      try {
        // 1. 直接调用停止设备任务API（最重要的步骤）
        console.log(`🛑 [XiaohongshuAutomation] 调用停止设备任务API`)
        console.log(`🛑 [XiaohongshuAutomation] API请求参数:`, {
          deviceId: deviceId,
          reason: '设备离线'
        })

        const response = await this.$http.post('/api/xiaohongshu/stop-device-tasks', {
          deviceId: deviceId,
          reason: '设备离线'
        })

        console.log(`🛑 [XiaohongshuAutomation] API响应:`, response.data)

        if (response.data.success) {
          console.log(`🛑 [XiaohongshuAutomation] ✅ 执行日志状态更新成功，更新了 ${response.data.updatedCount} 条记录`)
          this.addLog('系统', `设备 ${deviceId} 的 ${response.data.updatedCount} 个任务已停止`, 'warning')
        } else {
          console.error(`🛑 [XiaohongshuAutomation] ❌ 执行日志状态更新失败:`, response.data.message)
          this.addLog('系统', `停止设备任务失败: ${response.data.message}`, 'error')
        }

        // 2. 停止手机端脚本
        console.log(`🛑 [XiaohongshuAutomation] 发送停止脚本命令到设备: ${deviceId}`)
        const socket = this.$store.getters['socket/socket']
        if (socket && socket.connected) {
          socket.emit('script_command', {
            type: 'stop_script',
            deviceId: deviceId,
            reason: '设备离线'
          })
          console.log(`🛑 [XiaohongshuAutomation] ✅ 停止脚本命令已发送`)
        } else {
          console.log(`🛑 [XiaohongshuAutomation] ❌ Socket未连接，无法发送停止脚本命令`)
        }

        // 3. 更新Vuex状态
        console.log(`🛑 [XiaohongshuAutomation] 更新Vuex状态`)
        const runningTasks = this.$store.getters['xiaohongshu/getRunningTasks']
        console.log(`🛑 [XiaohongshuAutomation] 当前运行任务:`, runningTasks)

        for (const functionType of runningTasks) {
          console.log(`🛑 [XiaohongshuAutomation] 停止功能 ${functionType} 的Vuex状态`)
          this.$store.dispatch('xiaohongshu/stopTask', {
            functionType: functionType,
            reason: 'device_offline'
          })

          // 发送任务停止事件到组件
          this.$root.$emit('xiaohongshu-task-stopped', {
            functionType: functionType,
            deviceId: deviceId,
            reason: 'device_offline',
            message: '设备离线，任务已停止'
          })
        }

        // 4. 清理该设备的执行状态
        this.clearDeviceExecutionState(deviceId)

        console.log(`🛑 [XiaohongshuAutomation] ✅ 设备 ${deviceId} 脚本停止和状态更新完成`)

      } catch (error) {
        console.error(`🛑 [XiaohongshuAutomation] ❌ 停止设备脚本失败:`, error)
        console.error(`🛑 [XiaohongshuAutomation] 错误详情:`, error.response?.data || error.message)
        this.addLog('系统', `停止设备脚本失败: ${error.message}`, 'error')
      }
    },



    // 初始化调试消息监听器
    initDebugMessageListener() {
      window.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'DEBUG_GET_STATE' && event.data.source === 'debug-tool') {
          console.log('🔧 [XiaohongshuAutomation] 收到调试工具状态查询请求')

          // 发送当前状态给调试工具
          window.postMessage({
            type: 'DEBUG_STATE_RESPONSE',
            state: {
              selectedFunction: this.selectedFunction,
              selectedDevices: this.selectedDevices,
              deviceConfigs: Object.keys(this.deviceConfigs),
              activeDeviceTab: this.activeDeviceTab,
              executing: this.executing,
              allDevices: this.$store.getters['device/devices']?.map(d => ({
                id: d.device_id,
                name: d.device_name,
                status: d.status
              })) || []
            }
          }, '*')

          console.log('🔧 [XiaohongshuAutomation] 已发送状态响应给调试工具')
        }
      })
    },

    // 清理指定设备的执行状态
    clearDeviceExecutionState(deviceId) {
      console.log(`清理设备 ${deviceId} 的执行状态`)

      // 通过Vuex清理该设备相关的执行状态
      this.$store.dispatch('xiaohongshu/clearDeviceExecutionState', deviceId)

      // 如果当前没有其他设备在执行，重置全局状态
      const allDevices = this.getAllDevices() || []
      const hasRunningDevices = allDevices.some(device =>
        device.status === 'busy' && device.device_id !== deviceId
      )

      if (!hasRunningDevices) {
        console.log('没有其他设备在执行，重置全局执行状态')
        this.$store.dispatch('xiaohongshu/resetGlobalExecutionState')
      }

      this.addLog('系统', `已清理设备 ${deviceId} 的执行状态`, 'info')
      console.log(`🔴 [XiaohongshuAutomation] ✅ 设备执行状态清理完成`)
    },

    // 强制清理设备在Vuex中的所有功能状态
    forceCleanDeviceFromVuex(deviceId) {
      console.log(`🔴 [XiaohongshuAutomation] 强制清理设备 ${deviceId} 在Vuex中的状态`)

      // 获取所有功能类型
      const functionTypes = [
        'profile_modification',  // 修改资料
        'group_search_join',     // 搜索群聊加群发消息
        'group_message_loop',    // 循环群发消息
        'article_comment',       // 搜索文章评论
        'uid_private_message',   // UID私信
        'uid_file_message'       // UID文件私信
      ]

      functionTypes.forEach(functionType => {
        console.log(`🔴 [XiaohongshuAutomation] 检查功能 ${functionType} 中的设备状态`)

        // 获取当前功能状态
        const functionState = this.$store.getters['xiaohongshu/getFunctionState'](functionType)

        if (functionState && functionState.selectedDevices && functionState.selectedDevices.includes(deviceId)) {
          console.log(`🔴 [XiaohongshuAutomation] 在功能 ${functionType} 中找到设备 ${deviceId}，开始清理`)

          // 从选中设备列表中移除该设备
          const updatedDevices = functionState.selectedDevices.filter(id => id !== deviceId)

          if (updatedDevices.length === 0) {
            // 如果没有其他设备，重置整个功能状态
            console.log(`🔴 [XiaohongshuAutomation] 功能 ${functionType} 没有其他设备，重置功能状态`)
            this.$store.dispatch('xiaohongshu/setFunctionState', {
              functionType,
              stateData: {
                isScriptRunning: false,
                isScriptCompleted: false,
                taskId: null,
                config: {},
                selectedDevices: [],
                startTime: null,
                progress: 0,
                status: 'idle',
                logs: [],
                lastResult: null,
                executionStatus: 'stopped',
                executionMessage: '设备断开连接'
              }
            })
          } else {
            // 只更新选中设备列表
            console.log(`🔴 [XiaohongshuAutomation] 功能 ${functionType} 还有其他设备，只移除断开的设备`)
            this.$store.dispatch('xiaohongshu/setFunctionState', {
              functionType,
              stateData: {
                ...functionState,
                selectedDevices: updatedDevices
              }
            })
          }

          console.log(`🔴 [XiaohongshuAutomation] ✅ 功能 ${functionType} 中设备 ${deviceId} 状态清理完成`)
        }
      })

      console.log(`🔴 [XiaohongshuAutomation] ✅ 设备 ${deviceId} 在Vuex中的所有功能状态清理完成`)
    },

    // 处理设备状态更新事件（性能优化）
    handleDeviceStatusUpdate(data) {
      console.log('🟢 [XiaohongshuAutomation] 收到设备状态更新事件:', data)

      // 只在状态真正变化时才刷新
      if (data && data.deviceId) {
        // 更新store中的设备状态
        this.$store.dispatch('device/updateDeviceStatus', data)

        // 检查设备是否变为离线状态
        if (data.status === 'offline') {
          console.log('🟢 [XiaohongshuAutomation] 检测到设备状态变为离线，触发设备离线处理')
          this.handleDeviceDisconnected(data.deviceId)
        }

        // 防抖刷新设备列表
        this.debouncedLoadDevices()
      }
    },



    // 处理所有任务停止事件
    handleAllTasksStopped(data) {
      console.log('🛑 [XiaohongshuAutomation] 收到所有任务停止事件:', data)
      this.addLog('系统', '服务器通知: 所有任务已停止', 'warning')

      // 重置执行状态
      this.executing = false

      // ❌ 不要清空所有选中的设备！这会导致正在使用其他功能的设备也被清除
      // this.selectedDevices = []

      // 重置功能配置（但保留设备选择）
      this.functionConfig = {}
      this.selectedFunction = ''

      // 发送任务停止事件到各个组件
      this.$root.$emit('xiaohongshu-task-stopped', {
        functionType: 'all'
      })

      console.log('🛑 [XiaohongshuAutomation] 所有任务停止处理完成，保留设备选择状态')
      this.addLog('系统', '所有任务已停止，执行状态已重置', 'info')
    },

    // 处理按功能类型批量停止事件
    handleFunctionTasksStopped(data) {
      console.log('小红书自动化页面: 收到功能批量停止事件', data)

      const { functionType, stoppedDevices, updatedTasks, deviceList } = data
      const functionName = this.getFunctionTypeName(functionType)

      this.addLog('系统', `服务器通知: ${functionName}功能的所有任务已停止 (${stoppedDevices}个设备)`, 'warning')

      // 通过Vuex重置指定功能的状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: functionType,
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          executionStatus: 'stopped',
          executionMessage: '批量停止',
          progress: 0,
          deviceStates: {} // 清空所有设备状态
        }
      })

      // 发送功能停止事件到对应的配置组件
      this.$root.$emit('xiaohongshu-task-stopped', {
        functionType: functionType,
        reason: 'batch_stop',
        stoppedDevices: stoppedDevices,
        deviceList: deviceList
      })

      this.addLog('系统', `${functionName}功能批量停止完成，已更新${updatedTasks}个执行记录`, 'info')
    },

    // 处理功能状态清理事件
    handleClearFunctionState(data) {
      console.log('小红书自动化页面: 收到功能状态清理事件', data)

      const { functionType, reason } = data
      const functionName = this.getFunctionTypeName(functionType)

      this.addLog('系统', `服务器通知: 清理${functionName}功能状态 - ${reason}`, 'info')

      // 通过Vuex清理指定功能的状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: functionType,
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          executionStatus: 'idle',
          executionMessage: '',
          progress: 0,
          taskId: null,
          deviceStates: {} // 清空所有设备状态
        }
      })

      // 发送功能清理事件到对应的配置组件
      this.$root.$emit('xiaohongshu-function-cleared', {
        functionType: functionType,
        reason: reason
      })

      console.log(`功能状态清理完成: ${functionType}`)
    },

    // 处理任务更新事件（性能优化）
    handleTaskUpdate(data) {
      const { deviceId, status, message } = data

      if (status === 'device_disconnected') {
        // 从选中设备列表中移除离线设备
        const selectedIndex = this.selectedDevices.indexOf(deviceId)
        if (selectedIndex !== -1) {
          this.selectedDevices.splice(selectedIndex, 1)
          this.addLog('系统', `设备 ${deviceId} 已断开连接`, 'error')
        }
        return
      }

      if (status === 'stopped') {
        this.addLog('系统', `设备 ${deviceId} 任务已停止`, 'warning')
        // 发送任务停止事件到各个组件
        this.$root.$emit('xiaohongshu-task-stopped', {
          functionType: this.selectedFunction,
          deviceId: deviceId
        })
      }
    },

    // 处理脚本状态更新事件
    handleScriptStatusUpdate(data) {
      console.log('🔥 小红书自动化页面: 收到脚本状态更新', data)
      const { deviceId, status, message, stage } = data

      // 记录状态变化日志（只记录重要状态变化）
      if (stage === 'starting' || stage === 'completed' || stage === 'error' || stage === 'stopped') {
        const statusText = stage === 'starting' ? '等待开始' :
                          stage === 'completed' ? '完成' :
                          stage === 'error' ? '脚本出错' :
                          stage === 'stopped' ? '已停止' : status

        this.addLog('系统', `设备 ${deviceId}: ${statusText} - ${message}`,
                   statusText === '脚本出错' ? 'error' : 'info')

        // 发送任务状态事件到各个组件
        if (stage === 'starting') {
          // 注意：不在这里发送任务开始事件，等待后端发送包含logId和taskId的事件
          console.log(`=== 脚本状态更新：${stage}，等待后端发送任务开始事件 ===`)
        } else if (stage === 'completed') {
          // 检查是否是真正的脚本执行完成（通过message判断）
          const isScriptCompleted = message && (
            message.includes('脚本执行完成') ||
            message.includes('执行完成') ||
            message.includes('完成')
          )

          if (isScriptCompleted) {
            console.log(`=== 脚本执行完成：${stage}，立即发送脚本完成事件 ===`)
            // 脚本真正完成，立即重置状态
            this.$root.$emit('xiaohongshu-task-stopped', {
              functionType: this.selectedFunction,
              deviceId: deviceId
            })
            this.addLog('系统', '脚本执行完成，状态已重置', 'success')
          } else {
            console.log(`=== 步骤完成：${stage}，但不发送脚本完成事件，等待真正的脚本完成信号 ===`)
            // 中间步骤完成，延迟重置（保持原有逻辑）
            setTimeout(() => {
              this.$root.$emit('xiaohongshu-task-stopped', {
                functionType: this.selectedFunction,
                deviceId: deviceId
              })
              this.addLog('系统', '状态已自动重置，可以重新执行', 'info')
            }, 60000) // 60秒后重置
          }
        } else if (stage === 'error' || stage === 'stopped') {
          // 脚本出错或手动停止，立即重置状态
          this.$root.$emit('xiaohongshu-task-stopped', {
            functionType: this.selectedFunction,
            deviceId: deviceId
          })
        }
      }
    },

    // 处理调试日志事件
    handleDebugLog(data) {
      console.log('📝 小红书自动化页面: 收到调试日志', data)
      console.log('📝 当前时间:', new Date().toLocaleTimeString())
      const { deviceId, message, level } = data

      // 查找对应的设备
      const device = this.onlineDevices.find(d => d.device_id === deviceId)
      const deviceName = device ? device.device_name : deviceId

      // 根据日志级别确定显示类型
      let logType = 'info'
      if (level === 'error') {
        logType = 'error'
      } else if (level === 'warning' || level === 'warn') {
        logType = 'warning'
      }

      // 添加到执行日志中，带设备标识
      this.addLog(`${deviceName}`, message, logType)
    },

    // 处理脚本执行完成事件
    handleExecutionCompleted(data) {
      console.log('🎯 [XiaohongshuAutomation] 收到脚本执行完成事件:', data)
      console.log('🎯 [XiaohongshuAutomation] 当前选中功能:', this.selectedFunction)
      console.log('🎯 [XiaohongshuAutomation] 当前选中设备:', this.selectedDevices)

      try {
        // 根据taskId或deviceId确定功能类型
        const functionTypeMap = {
          'profile': '修改资料',
          'searchGroupChat': '搜索加群',
          'groupMessage': '循环群发',
          'articleComment': '文章评论'
        }

        // 尝试从当前选中的功能确定类型
        let functionType = this.selectedFunction

        // 如果没有选中功能，尝试从Vuex状态中查找正在运行的任务
        if (!functionType) {
          const runningTasks = this.$store.getters['xiaohongshu/getRunningTasks']
          if (runningTasks.length > 0) {
            functionType = runningTasks[0]
          }
        }

        if (functionType) {
          console.log(`[小红书自动化] 处理 ${functionType} 功能的执行完成事件`)

          // 更新Vuex状态
          this.$store.dispatch('xiaohongshu/stopTask', {
            functionType: functionType,
            reason: data.status === 'success' ? 'completed' : 'failed'
          })

          // 发送任务停止事件到组件
          this.$root.$emit('xiaohongshu-task-stopped', {
            functionType: functionType,
            reason: data.status === 'success' ? 'completed' : 'failed',
            message: data.message,
            timestamp: data.timestamp
          })

          // 如果是成功完成，发送脚本完成事件
          if (data.status === 'success') {
            this.$root.$emit('xiaohongshu-script-completed', {
              functionType: functionType,
              message: data.message,
              timestamp: data.timestamp
            })
          }

          // 添加日志
          const logType = data.status === 'success' ? 'success' : 'error'
          const logMessage = data.status === 'success' ? '脚本执行完成' : '脚本执行失败'
          this.addLog('系统', `${functionTypeMap[functionType] || functionType}: ${logMessage}`, logType)

          console.log(`[小红书自动化] ${functionType} 功能状态已重置`)
        } else {
          console.warn('[小红书自动化] 无法确定功能类型，跳过状态重置')
        }
      } catch (error) {
        console.error('[小红书自动化] 处理执行完成事件失败:', error)
      }
    },

    // 处理脚本停止完成事件
    handleScriptCompleted(data) {
      console.log('[小红书自动化] 收到脚本停止完成事件:', data)

      try {
        const { functionType, deviceId, taskId, status, message } = data

        // 发送脚本完成事件到对应的组件
        this.$root.$emit('xiaohongshu-script-completed', {
          functionType,
          deviceId,
          taskId,
          status,
          message
        })

        // 更新Vuex状态
        if (functionType) {
          this.$store.dispatch('xiaohongshu/setFunctionState', {
            functionType,
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: status === 'stopped',
              config: {}
            }
          })
        }

        // 添加日志
        const logType = status === 'stopped' ? 'warning' : 'info'
        const logMessage = status === 'stopped' ? '脚本已停止' : '脚本已完成'
        this.addLog('系统', `${functionType}: ${logMessage} - ${message}`, logType)

        console.log(`[小红书自动化] ${functionType} 脚本停止事件已处理`)
      } catch (error) {
        console.error('[小红书自动化] 处理脚本停止完成事件失败:', error)
      }
    },

    // 处理从后端发送的任务开始事件
    handleTaskStartedFromBackend(data) {
      try {
        console.log('[小红书自动化] 收到后端任务开始事件:', data)
        console.log('[小红书自动化] WebSocket连接状态:', this.socket ? this.socket.connected : '未连接')

        // 发送任务开始事件到组件，包含logId和taskId
        this.$root.$emit('xiaohongshu-task-started', {
          functionType: data.functionType,
          deviceId: data.deviceId,
          taskId: data.taskId,
          logId: data.logId,
          message: data.message
        })

        console.log('[小红书自动化] 已转发任务开始事件到组件:', {
          functionType: data.functionType,
          deviceId: data.deviceId,
          taskId: data.taskId,
          logId: data.logId
        })

        // 添加日志
        this.addLog('系统', `任务开始: ${data.message}`, 'info')

        // 延迟一下再发送一次，确保组件能接收到
        setTimeout(() => {
          console.log('[小红书自动化] 延迟发送任务开始事件')
          this.$root.$emit('xiaohongshu-task-started', {
            functionType: data.functionType,
            deviceId: data.deviceId,
            taskId: data.taskId,
            logId: data.logId,
            message: data.message,
            delayed: true
          })
        }, 500)

      } catch (error) {
        console.error('[小红书自动化] 处理后端任务开始事件失败:', error)
      }
    },



    // 恢复任务状态
    restoreTaskState(task) {
      // 检查任务中的设备是否还在线
      const onlineDeviceIds = this.onlineDevices.map(d => d.device_id)
      const taskDevices = task.devices || []
      const onlineTaskDevices = taskDevices.filter(deviceId => onlineDeviceIds.includes(deviceId))

      console.log('任务设备:', taskDevices)
      console.log('在线设备:', onlineDeviceIds)
      console.log('在线的任务设备:', onlineTaskDevices)

      // 如果没有在线的任务设备，不恢复任务状态
      if (onlineTaskDevices.length === 0) {
        this.addLog('系统', `任务 ${task.id} 的设备都已离线，不恢复任务状态`, 'warning')
        return
      }

      // 如果只有部分设备在线，只恢复在线设备
      if (onlineTaskDevices.length < taskDevices.length) {
        this.addLog('系统', `任务 ${task.id} 部分设备离线，只恢复在线设备`, 'warning')
      }

      // 恢复功能选择
      if (task.function) {
        this.selectedFunction = task.function
      }

      // 只恢复在线的设备选择
      this.selectedDevices = [...onlineTaskDevices]
      console.log('已恢复在线设备选择:', this.selectedDevices)

      // 恢复配置
      if (task.config) {
        this.functionConfig = { ...task.config }
        console.log('已恢复功能配置:', this.functionConfig)
      }



      this.addLog('系统', `已恢复任务状态: ${task.id}`, 'success')
      this.addLog('系统', `功能: ${task.function}, 设备数: ${task.devices ? task.devices.length : 0}`, 'info')
    },

    selectFunction(key) {
      console.log('=== selectFunction 被调用 ===')
      console.log('选择的功能:', key)
      console.log('当前功能:', this.selectedFunction)

      if (this.selectedFunction !== key) {
        console.log('功能发生变化，开始切换')
        this.selectedFunction = key
        this.functionConfig = {} // 清空配置，等待用户输入
        console.log('已清空 functionConfig:', this.functionConfig)

        // 切换功能时清除设备选择
        this.selectedDevices = []
        this.activeDeviceTab = ''
        this.deviceConfigs = {}
        
        console.log('[功能切换] 已清除设备选择，等待重新选择设备')

        // 使用nextTick确保DOM更新完成
        this.$nextTick(() => {
          console.log('=== nextTick 中的检查 ===')
          console.log('切换到功能:', key)
          console.log('当前 selectedFunction:', this.selectedFunction)
          console.log('getComponentName() 返回:', this.getComponentName())
          console.log('设备配置:', this.deviceConfigs)
          console.log('已清空配置，等待用户输入')
          
          // 检查是否有正在执行当前功能的设备，如果有则自动选择
          this.autoRestoreExecutingDevices()
        })
      } else {
        console.log('功能未变化，跳过切换')
      }
    },

    getCurrentFunction() {
      return this.functions.find(f => f.key === this.selectedFunction) || {}
    },

    getComponentName() {
      console.log('=== getComponentName 被调用 ===')
      console.log('selectedFunction:', this.selectedFunction)

      if (!this.selectedFunction) {
        console.log('selectedFunction 为空，返回 null')
        return null
      }

      const componentMap = {
        'profile': 'ProfileConfig',
        'searchGroupChat': 'SearchGroupChatConfig',
        'groupMessage': 'GroupMessageConfigOriginal',
        'articleComment': 'ArticleCommentConfig',
        'uidMessage': 'UidMessageConfig',
        'uidFileMessage': 'UidFileMessageConfig',
        'videoPublish': 'VideoPublishConfig'
      }

      const componentName = componentMap[this.selectedFunction] || null
      console.log('返回的组件名:', componentName)
      return componentName
    },

    updateConfig(config) {
      console.log('=== Vue主组件 updateConfig 被调用 ===')
      console.log('收到的配置:', JSON.stringify(config, null, 2))
      console.log('当前 functionConfig:', JSON.stringify(this.functionConfig, null, 2))
      console.log('selectedFunction:', this.selectedFunction)

      // 防止无限循环，只在配置真正改变时更新
      if (config && typeof config === 'object') {
        const newConfigStr = JSON.stringify(config)
        const oldConfigStr = JSON.stringify(this.functionConfig)

        console.log('配置比较:', { new: newConfigStr, old: oldConfigStr })

        if (newConfigStr !== oldConfigStr) {
          this.functionConfig = { ...config }
          console.log('配置已更新:', this.selectedFunction, config)
          console.log('更新后的 functionConfig:', this.functionConfig)
        } else {
          console.log('配置未改变，跳过更新')
        }
      } else {
        console.log('收到的配置无效:', config)
      }
    },

    handleValidationError(errors) {
      console.warn('配置验证错误:', errors)
      // 可以在这里显示错误提示
    },

    handleValidationSuccess() {
      console.log('配置验证成功')
    },

    async executeFunction() {
      if (!this.selectedFunction || this.selectedDevices.length === 0) {
        this.$message.warning('请选择功能和执行设备')
        return
      }

      this.executing = true
      this.addLog('系统', '开始执行小红书自动化任务...', 'info')

      try {
        // 添加详细的调试日志
        console.log('=== Vue前端执行前的配置检查 ===')
        console.log('selectedFunction:', this.selectedFunction)
        console.log('functionConfig:', JSON.stringify(this.functionConfig, null, 2))
        console.log('scheduleConfig:', this.scheduleConfig)
        console.log('selectedDevices:', this.selectedDevices)

        // 特别检查群聊功能的配置
        if (this.selectedFunction === 'groupChat') {
          console.log('=== 群聊功能特别检查 ===')
          console.log('searchKeyword:', this.functionConfig.searchKeyword)
          console.log('targetJoinCount:', this.functionConfig.targetJoinCount)
          console.log('functionConfig是否为空对象:', Object.keys(this.functionConfig).length === 0)
        }

        // 构建执行参数 - 支持多设备独立配置
        const params = {
          functionType: this.selectedFunction,
          config: this.functionConfig, // 保留作为兼容性
          deviceConfigs: this.deviceConfigs, // 新增：每个设备的独立配置
          schedule: this.scheduleConfig,
          deviceIds: this.selectedDevices
        }

        console.log('=== Vue前端发送的完整参数 ===')
        console.log(JSON.stringify(params, null, 2))

        // 发送执行请求
        const response = await this.$http.post('/api/xiaohongshu/execute', params)

        if (response.data.success) {
          this.addLog('系统', '任务下发成功', 'success')

          // 更新Vuex状态
          await this.$store.dispatch('xiaohongshu/startTask', {
            functionType: this.selectedFunction,
            selectedDevices: this.selectedDevices,
            config: this.functionConfig,
            taskId: response.data.taskId || Date.now().toString()
          })

          // 立即发送任务开始事件，确保配置组件状态更新
          console.log('=== 立即发送任务开始事件，确保配置组件状态更新 ===')
          this.$root.$emit('xiaohongshu-task-started', {
            functionType: this.selectedFunction,
            deviceId: this.selectedDevices[0],
            taskId: response.data.taskId || Date.now().toString(),
            logId: `${response.data.taskId || Date.now().toString()}_${this.selectedDevices[0]}`,
            message: `开始执行${this.getFunctionTypeName(this.selectedFunction)}任务`,
            immediate: true // 标记为立即发送的事件
          })

          console.log('=== 任务开始事件已发送 ===')
          console.log('功能类型:', this.selectedFunction)
          console.log('设备ID:', this.selectedDevices[0])
          console.log('配置参数:', this.functionConfig)
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        this.addLog('系统', `执行失败: ${error.message}`, 'error')
        this.$message.error('执行失败: ' + error.message)
      } finally {
        this.executing = false
      }
    },
    // 执行单个设备
    async executeForDevice(deviceId) {
      if (!this.selectedFunction || !deviceId) {
        this.$message.warning('请选择功能和设备')
        return
      }

      const config = this.deviceConfigs[deviceId]
      if (!config || !this.isDeviceConfigValid(deviceId)) {
        this.$message.warning('请完善设备配置参数')
        return
      }

      this.executing = true
      const deviceName = this.getDeviceName(deviceId)
      this.addLog('系统', `开始执行设备 ${deviceName} 的任务...`, 'info')

      try {
        console.log(`=== 执行单个设备 ${deviceId} ===`)
        console.log('设备配置:', config)

        // 生成前端taskId，确保与服务器同步
        const frontendTaskId = `xiaohongshu_${this.selectedFunction}_${Date.now()}_${deviceId}`

        // 构建单设备执行参数
        const params = {
          functionType: this.selectedFunction,
          config: config,
          deviceConfigs: { [deviceId]: config },
          schedule: this.scheduleConfig,
          deviceIds: [deviceId],
          taskId: frontendTaskId  // 添加前端生成的taskId
        }

        console.log('单设备执行参数:', JSON.stringify(params, null, 2))

        // 发送执行请求
        const response = await this.$http.post('/api/xiaohongshu/execute', params)

        if (response.data.success) {
          this.addLog('系统', `设备 ${deviceName} 任务下发成功`, 'success')

          // 使用前端生成的taskId，确保与脚本中的taskId一致
          const taskId = frontendTaskId

          // 更新Vuex状态
          await this.$store.dispatch('xiaohongshu/startTask', {
            functionType: this.selectedFunction,
            selectedDevices: [deviceId],
            config: config,
            taskId: taskId
          })

          // 立即发送任务开始事件，确保子组件状态更新
          // 使用与服务器端一致的taskId格式
          const deviceTaskId = `${frontendTaskId}_${deviceId}`
          this.$root.$emit('xiaohongshu-task-started', {
            functionType: this.selectedFunction,
            deviceId: deviceId,
            taskId: deviceTaskId,
            logId: deviceTaskId,
            config: config,
            message: `开始执行${this.getCurrentFunction().name}任务`
          })

          console.log(`=== 设备 ${deviceId} 任务开始事件已发送 ===`)
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('执行单设备任务失败:', error)
        this.addLog('系统', `设备 ${deviceName} 执行失败: ${error.message}`, 'error')
        this.$message.error(`设备 ${deviceName} 执行失败: ` + error.message)
      } finally {
        this.executing = false
      }
    },

    // 批量执行所有设备
    async executeAllDevices() {
      if (!this.selectedFunction || this.selectedDevices.length === 0) {
        this.$message.warning('请选择功能和执行设备')
        return
      }

      const validDevices = this.selectedDevices.filter(deviceId => this.isDeviceConfigValid(deviceId))
      if (validDevices.length === 0) {
        this.$message.warning('没有有效的设备配置，请完善配置参数')
        return
      }

      this.executing = true
      this.addLog('系统', `开始批量执行 ${validDevices.length} 个设备的任务...`, 'info')

      try {
        console.log('=== 批量执行所有设备 ===')
        console.log('有效设备:', validDevices)
        console.log('设备配置:', this.deviceConfigs)

        // 特殊处理视频发布功能 - 为每个设备单独执行
        if (this.selectedFunction === 'videoPublish') {
          console.log('🎬 [批量执行] 检测到视频发布功能，为每个设备单独执行')

          // 生成批量执行的基础taskId
          const batchTaskId = `xiaohongshu_videoPublish_batch_${Date.now()}`

          for (const deviceId of validDevices) {
            try {
              // 为每个设备生成基于批量taskId的独立taskId
              const deviceTaskId = `${batchTaskId}_${deviceId}`
              console.log(`🎯 [批量执行] 设备 ${deviceId} 生成taskId:`, deviceTaskId)

              // 构建单个设备的执行参数
              const deviceParams = {
                functionType: 'videoPublish',
                config: this.deviceConfigs[deviceId],
                deviceConfigs: { [deviceId]: this.deviceConfigs[deviceId] },
                schedule: this.scheduleConfig,
                deviceIds: [deviceId],
                taskId: batchTaskId  // 传递基础taskId，服务器会为每个设备生成唯一ID
              }

              console.log(`📋 [批量执行] 设备 ${deviceId} 执行参数:`, deviceParams)

              // 发送单个设备的执行请求
              const deviceResponse = await this.$http.post('/api/xiaohongshu/execute', deviceParams)

              if (deviceResponse.data.success) {
                this.addLog('系统', `设备 ${this.getDeviceName(deviceId)} 视频发布任务已启动`, 'success')

                // 更新Vuex状态（使用合并模式）
                await this.$store.dispatch('xiaohongshu/startTask', {
                  functionType: 'videoPublish',
                  selectedDevices: [deviceId],
                  config: this.deviceConfigs[deviceId],
                  taskId: deviceTaskId,
                  mergeDevices: true // 启用合并模式
                })

                // 发送任务开始事件到对应的视频发布配置组件
                this.$root.$emit('xiaohongshu-task-started', {
                  functionType: 'videoPublish',
                  deviceId: deviceId,
                  taskId: deviceTaskId,
                  logId: deviceTaskId + '_' + deviceId,
                  config: this.deviceConfigs[deviceId],
                  message: `开始执行视频发布任务`
                })

                console.log(`✅ [批量执行] 设备 ${deviceId} 任务开始事件已发送，taskId: ${deviceTaskId}`)
              } else {
                throw new Error(deviceResponse.data.message)
              }
            } catch (deviceError) {
              console.error(`❌ [批量执行] 设备 ${deviceId} 执行失败:`, deviceError)
              this.addLog('系统', `设备 ${this.getDeviceName(deviceId)} 执行失败: ${deviceError.message}`, 'error')
            }
          }

          this.addLog('系统', `批量视频发布任务已全部下发，共 ${validDevices.length} 个设备`, 'success')
        } else {
          // 其他功能使用原有的批量执行逻辑
          const validDeviceConfigs = {}
          validDevices.forEach(deviceId => {
            validDeviceConfigs[deviceId] = this.deviceConfigs[deviceId]
          })

          // 生成批量执行的taskId
          const batchTaskId = `xiaohongshu_${this.selectedFunction}_batch_${Date.now()}`

          const params = {
            functionType: this.selectedFunction,
            config: this.functionConfig, // 保留兼容性
            deviceConfigs: validDeviceConfigs,
            schedule: this.scheduleConfig,
            deviceIds: validDevices,
            taskId: batchTaskId  // 添加批量taskId
          }

          console.log('批量执行参数:', JSON.stringify(params, null, 2))

          // 发送执行请求
          const response = await this.$http.post('/api/xiaohongshu/execute', params)

          if (response.data.success) {
            this.addLog('系统', `批量任务下发成功，共 ${validDevices.length} 个设备`, 'success')

            // 使用前端生成的batchTaskId，确保与脚本中的taskId一致
            const taskId = batchTaskId

            // 首先为所有设备一次性更新Vuex状态
            await this.$store.dispatch('xiaohongshu/startTask', {
              functionType: this.selectedFunction,
              selectedDevices: validDevices, // 传递所有设备
              config: this.deviceConfigs[validDevices[0]], // 使用第一个设备的配置作为基础
              taskId: taskId
            })

            // 为每个设备发送任务开始事件
            for (const deviceId of validDevices) {
              // 使用与服务器端一致的taskId格式
              const deviceTaskId = `${batchTaskId}_${deviceId}`
              // 立即发送任务开始事件，确保设备状态更新
              this.$root.$emit('xiaohongshu-task-started', {
                functionType: this.selectedFunction,
                deviceId: deviceId,
                taskId: deviceTaskId,
                logId: deviceTaskId,
                config: this.deviceConfigs[deviceId],
                message: `开始执行${this.getCurrentFunction().name}任务`
              })

              console.log(`=== 设备 ${deviceId} 任务开始事件已发送，taskId: ${deviceTaskId} ===`)
            }
          } else {
            throw new Error(response.data.message)
          }
        }
      } catch (error) {
        console.error('批量执行任务失败:', error)
        this.addLog('系统', `批量执行失败: ${error.message}`, 'error')
        this.$message.error('批量执行失败: ' + error.message)
      } finally {
        this.executing = false
      }
    },

    // 复制配置到所有设备
    copyConfigToAll() {
      console.log('🔧 [小红书] 开始复制配置到所有设备...')
      console.log('🔧 [小红书] 当前活动设备:', this.activeDeviceTab)
      console.log('🔧 [小红书] 选中的设备:', this.selectedDevices)
      console.log('🔧 [小红书] 当前功能:', this.selectedFunction)

      if (!this.activeDeviceTab || this.selectedDevices.length <= 1) {
        this.$message.warning('请选择要复制的设备配置')
        return
      }

      // 对于视频发布功能，使用组件引用方式复制配置
      if (this.selectedFunction === 'videoPublish') {
        console.log('🎬 [小红书] 检测到视频发布功能，使用组件引用方式复制配置')

        const sourceConfigRef = this.$refs[`config_${this.activeDeviceTab}`]
        console.log('🔧 [小红书] 源设备配置组件引用:', sourceConfigRef)

        if (!sourceConfigRef || !sourceConfigRef[0]) {
          this.$message.error('无法获取源设备配置组件')
          console.error('❌ [小红书] 无法获取源设备配置组件')
          return
        }

        const sourceConfig = sourceConfigRef[0].getConfig()
        console.log('📋 [小红书] 源设备配置:', sourceConfig)

        if (!sourceConfig || Object.keys(sourceConfig).length === 0) {
          this.$message.warning('源设备配置为空，无法复制')
          console.warn('⚠️ [小红书] 源设备配置为空')
          return
        }

        this.$confirm(`确定要将 ${this.getDeviceName(this.activeDeviceTab)} 的视频发布配置复制到其他所有设备吗？\n\n将复制：小红书应用、视频标题模板、视频描述、话题标签等参数\n视频选择不会被复制`, '确认复制', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let copiedCount = 0
          let failedDevices = []

          for (const deviceId of this.selectedDevices) {
            if (deviceId !== this.activeDeviceTab) {
              console.log(`🔧 [小红书] 正在复制配置到设备: ${deviceId}`)

              const targetConfigRef = this.$refs[`config_${deviceId}`]
              console.log(`🔧 [小红书] 设备 ${deviceId} 配置组件引用:`, targetConfigRef)

              if (targetConfigRef && targetConfigRef[0] && targetConfigRef[0].setConfig) {
                try {
                  targetConfigRef[0].setConfig(sourceConfig)
                  copiedCount++
                  console.log(`✅ [小红书] 配置已复制到设备: ${deviceId}`)

                  // 验证复制结果
                  const copiedConfig = targetConfigRef[0].getConfig()
                  console.log(`📋 [小红书] 设备 ${deviceId} 复制后的配置:`, copiedConfig)
                } catch (error) {
                  console.error(`❌ [小红书] 复制配置到设备 ${deviceId} 失败:`, error)
                  failedDevices.push(deviceId)
                }
              } else {
                console.error(`❌ [小红书] 设备 ${deviceId} 的配置组件无效`)
                failedDevices.push(deviceId)
              }
            }
          }

          console.log(`📊 [小红书] 复制结果: 成功 ${copiedCount} 个, 失败 ${failedDevices.length} 个`)

          if (copiedCount > 0) {
            let message = `已复制视频发布配置到 ${copiedCount} 个设备`
            if (failedDevices.length > 0) {
              message += `，${failedDevices.length} 个设备复制失败`
            }
            this.$message.success(message)
            this.addLog('系统', `视频发布配置复制完成，共复制到 ${copiedCount} 个设备`, 'info')
          } else {
            this.$message.error('配置复制失败，请检查设备状态')
            this.addLog('系统', '视频发布配置复制失败', 'error')
          }
        }).catch(() => {
          console.log('🔧 [小红书] 用户取消配置复制')
        })
      } else {
        // 对于其他功能，使用原有的deviceConfigs方式复制配置
        console.log('🔧 [小红书] 使用deviceConfigs方式复制配置')

        const sourceConfig = this.deviceConfigs[this.activeDeviceTab]
        if (!sourceConfig) {
          this.$message.warning('当前设备没有配置参数')
          return
        }

        this.$confirm(`确定要将 ${this.getDeviceName(this.activeDeviceTab)} 的配置复制到其他所有设备吗？`, '确认复制', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let copiedCount = 0
          this.selectedDevices.forEach(deviceId => {
            if (deviceId !== this.activeDeviceTab) {
              this.$set(this.deviceConfigs, deviceId, { ...sourceConfig })
              copiedCount++
            }
          })

          this.$message.success(`已将配置复制到 ${copiedCount} 个设备`)
          this.addLog('系统', `配置复制完成，共复制到 ${copiedCount} 个设备`, 'info')
        }).catch(() => {
          // 用户取消
        })
      }
    },


    // 处理子组件的脚本执行请求
    async handleExecuteScript(data) {
      console.log('[XiaohongshuAutomation] 收到脚本执行请求:', data)

      if (!this.selectedDevices.length) {
        this.$message.warning('请先选择执行设备')
        return
      }

      // 获取设备ID，优先使用传入的deviceId
      const currentDeviceId = data.deviceId || this.activeDeviceTab
      if (!currentDeviceId) {
        this.$message.warning('请选择要执行的设备')
        return
      }

      console.log('[XiaohongshuAutomation] 执行单个设备脚本:', currentDeviceId)

      // 如果是视频发布功能，需要特殊处理
      if (data.functionType === 'videoPublish') {
        await this.executeVideoPublishForDevice(currentDeviceId, data)
      } else {
        // 执行当前设备的脚本
        await this.executeForDevice(currentDeviceId)
      }
    },

    // 执行视频发布功能
    async executeVideoPublishForDevice(deviceId, data) {
      console.log('[XiaohongshuAutomation] 执行视频发布功能:', deviceId, data)

      const config = data.config || this.deviceConfigs[deviceId]
      if (!config) {
        this.$message.warning('请完善设备配置参数')
        return
      }

      this.executing = true
      const deviceName = this.getDeviceName(deviceId)
      this.addLog('系统', `开始执行设备 ${deviceName} 的视频发布任务...`, 'info')

      try {
        // 生成前端taskId，确保与服务器同步
        const frontendTaskId = `xiaohongshu_videoPublish_${Date.now()}_${deviceId}`

        // 构建视频发布执行参数
        const params = {
          functionType: 'videoPublish',
          config: config,
          deviceConfigs: { [deviceId]: config },
          schedule: this.scheduleConfig,
          deviceIds: [deviceId],
          taskId: frontendTaskId // 使用前端生成的taskId
        }

        console.log('视频发布执行参数:', JSON.stringify(params, null, 2))

        // 发送执行请求
        const response = await this.$http.post('/api/xiaohongshu/execute', params)

        if (response.data.success) {
          this.$message.success(`设备 ${deviceName} 视频发布任务已启动`)
          this.addLog('系统', `设备 ${deviceName} 视频发布任务已启动`, 'success')

          // 发送任务开始事件到对应组件
          // 使用与服务器端一致的taskId格式
          const deviceTaskId = `${frontendTaskId}_${deviceId}`
          this.$root.$emit('xiaohongshu-task-started', {
            functionType: 'videoPublish',
            deviceId: deviceId,
            taskId: deviceTaskId,
            logId: deviceTaskId,
            config: config,
            message: `开始执行视频发布任务`
          })

          console.log(`=== 设备 ${deviceId} 视频发布任务开始事件已发送 ===`)
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('执行视频发布任务失败:', error)
        this.addLog('系统', `设备 ${deviceName} 视频发布执行失败: ${error.message}`, 'error')
        this.$message.error(`设备 ${deviceName} 视频发布执行失败: ` + error.message)
      } finally {
        this.executing = false
      }
    },





    addLog(device, message, level = 'info') {
      this.logs.unshift({
        time: new Date(),
        device,
        message,
        level
      })

      // 限制日志数量
      if (this.logs.length > 1000) {
        this.logs = this.logs.slice(0, 1000)
      }
    },

    clearLogs() {
      this.logs = []
    },

    // 跳转到执行日志页面
    goToLogs() {
      this.$router.push('/xiaohongshu-logs')
    },

    // 处理组件内部任务启动
    handleTaskStarted(taskInfo) {
      console.log('组件内部任务已启动:', taskInfo)
      this.addLog('系统', `组件内部任务已启动: ${taskInfo.taskId}`, 'success')
      // 注意：不在这里发送任务开始事件，等待后端发送包含logId和taskId的事件
      console.log('=== 组件内部任务启动，等待后端发送任务开始事件 ===', taskInfo)
    },

    // 处理组件内部任务停止
    handleTaskStopped(taskInfo) {
      console.log('组件内部任务已停止:', taskInfo)
      this.addLog('系统', `组件内部任务已停止: ${taskInfo.taskId}`, 'warning')
      // 发送任务停止事件到各个组件
      this.$root.$emit('xiaohongshu-task-stopped', {
        functionType: this.selectedFunction,
        taskInfo: taskInfo
      })
    },

    // 处理设备选择
    handleDeviceSelected(device) {
      if (device) {
        // 检查设备是否忙碌
        if (device.status === 'busy') {
          // 检查设备是否正在执行当前功能
          const isExecutingCurrentFunction = this.isDeviceExecutingCurrentFunction(device.device_id)
          
          if (!isExecutingCurrentFunction) {
            this.$message.warning(`设备 ${device.device_name} 正在执行其他脚本，无法选择`)
            this.addLog('系统', `尝试选择忙碌设备: ${device.device_name}，已拒绝`, 'warning')
            return
          } else {
            // 设备正在执行当前功能，允许选择
            this.addLog('系统', `设备 ${device.device_name} 正在执行当前功能，允许选择`, 'info')
          }
        }

        // 添加设备到选中列表（如果还没有选中）
        if (!this.selectedDevices.includes(device.device_id)) {
          this.selectedDevices.push(device.device_id)

          // 为新选中的设备初始化配置
          this.initDeviceConfig(device.device_id)

          // 如果是第一个设备，设置为活跃标签
          if (this.selectedDevices.length === 1) {
            this.activeDeviceTab = device.device_id
          }
        }
        this.addLog('系统', `已选择设备: ${device.device_name}`, 'info')
      }
    },

    // 处理设备移除
    handleDeviceRemoved(device) {
      if (device) {
        this.removeDevice(device.device_id)
        this.addLog('系统', `已移除设备: ${device.device_name}`, 'info')
      }
    },

    // 处理批量设备选择变化
    handleDevicesSelectionChanged(deviceIds) {
      console.log('[批量选择] 设备选择变化:', deviceIds)

      // 移除不在新列表中的设备
      const devicesToRemove = this.selectedDevices.filter(id => !deviceIds.includes(id))
      devicesToRemove.forEach(deviceId => {
        this.removeDevice(deviceId)
      })

      // 添加新选择的设备（过滤掉忙碌设备）
      const devicesToAdd = deviceIds.filter(id => !this.selectedDevices.includes(id))
      devicesToAdd.forEach(deviceId => {
        const device = this.getAllDevices().find(d => d.device_id === deviceId)
        if (device) {
          // 检查设备状态，忙碌设备会被handleDeviceSelected拒绝
          this.handleDeviceSelected(device)
        }
      })

      console.log('[批量选择] 最终选中设备:', this.selectedDevices)
    },

    // 移除设备
    removeDevice(deviceId) {
      const index = this.selectedDevices.indexOf(deviceId)
      if (index > -1) {
        this.selectedDevices.splice(index, 1)

        // 删除设备配置
        this.$delete(this.deviceConfigs, deviceId)

        // 如果移除的是当前活跃标签，切换到第一个设备
        if (this.activeDeviceTab === deviceId && this.selectedDevices.length > 0) {
          this.activeDeviceTab = this.selectedDevices[0]
        } else if (this.selectedDevices.length === 0) {
          this.activeDeviceTab = ''
        }

        const device = this.onlineDevices.find(d => d.device_id === deviceId)
        this.addLog('系统', `已移除设备: ${device ? device.device_name : deviceId}`, 'info')
      }
    },

    // 获取设备名称
    getDeviceName(deviceId) {
      const device = this.getAllDevices().find(d => d.device_id === deviceId)
      return device ? device.device_name : deviceId
    },

    // 初始化设备配置
    initDeviceConfig(deviceId) {
      if (!this.deviceConfigs[deviceId]) {
        // 使用默认配置模板初始化设备配置
        this.$set(this.deviceConfigs, deviceId, { ...this.getDefaultConfig() })
        console.log(`[多设备配置] 为设备 ${deviceId} 初始化配置:`, this.deviceConfigs[deviceId])
      }
    },

    // 获取默认配置模板
    getDefaultConfig() {
      const defaultConfigs = {
        profile: {
          nickname: '',
          profile: '',
          modifyOptions: [],
          operationDelay: 2,
          safetyOptions: []
        },
        searchGroupChat: {
          searchKeyword: '',
          targetJoinCount: 5,
          maxScrollAttempts: 10,
          enableDetailedLog: false
        },
        groupMessage: {
          sendInterval: 3600,
          enableLoop: false,
          maxLoopCount: 10
        },
        articleComment: {
          searchKeyword: '',
          commentCount: 3,
          operationDelay: 5
        },
        uidMessage: {
          inputMode: 'manual',
          uidList: '',
          uidStrategy: 'sequential',
          message: '',
          delay: 5,
          maxCount: 10,
          enableDetailLog: true,
          skipUsedUids: true,
          autoMarkUsed: true,
          advancedOptions: ['enableDetailLog', 'autoMarkUsed']
        }
      }
      return defaultConfigs[this.selectedFunction] || {}
    },

    // 处理设备配置更新
    handleDeviceConfigUpdate(deviceId, config) {
      console.log(`[多设备配置] 设备 ${deviceId} 配置更新:`, config)
      this.$set(this.deviceConfigs, deviceId, config)
    },

    // 获取设备标签页标签
    getDeviceTabLabel(deviceId) {
      const device = this.getAllDevices().find(d => d.device_id === deviceId)
      const deviceName = device ? device.device_name : deviceId
      const deviceIP = device ? this.getDeviceIP(device) : '未知IP'
      const isValid = this.isDeviceConfigValid(deviceId)
      return `${deviceName} (${deviceIP}) ${isValid ? '✓' : '⚠'}`
    },

    // 检查设备配置是否有效
    isDeviceConfigValid(deviceId) {
      const config = this.deviceConfigs[deviceId]
      if (!config) return false

      // 根据功能类型验证配置
      switch (this.selectedFunction) {
        case 'profile':
          return (config.nickname && config.nickname.trim()) || (config.profile && config.profile.trim())
        case 'searchGroupChat':
          return config.searchKeyword && config.searchKeyword.trim()
        case 'groupMessage':
          return config.sendInterval && config.sendInterval > 0
        case 'articleComment':
          return config.searchKeyword && config.searchKeyword.trim() && config.commentCount > 0
        case 'uidMessage':
          // UID私信需要更严格的验证
          let hasValidUids = false
          if (config.inputMode === 'manual') {
            if (typeof config.uidList === 'string') {
              const uidArray = config.uidList
                .split('\n')
                .map(uid => uid.trim())
                .filter(uid => uid.length > 0)
              hasValidUids = uidArray.length > 0
            } else if (Array.isArray(config.uidList)) {
              hasValidUids = config.uidList.length > 0
            }
          } else if (config.inputMode === 'file') {
            hasValidUids = true // 文件模式下服务器端处理
          }
          
          const hasValidMessage = config.message && config.message.trim().length > 0
          const hasValidDelay = config.delay && config.delay >= 3
          const hasValidMaxCount = config.maxCount && config.maxCount > 0
          
          console.log(`[设备配置验证] ${deviceId} UID私信验证:`, {
            hasValidUids,
            hasValidMessage,
            hasValidDelay,
            hasValidMaxCount
          })
          
          return hasValidUids && hasValidMessage && hasValidDelay && hasValidMaxCount
        default:
          return true
      }
    },

    // 获取有效配置的数量
    getValidConfigCount() {
      return this.selectedDevices.filter(deviceId => this.isDeviceConfigValid(deviceId)).length
    },

    // 检查是否有有效配置
    hasValidConfigs() {
      return this.getValidConfigCount() > 0
    },

    // 处理标签页点击
    handleTabClick(tab) {
      console.log(`[多设备配置] 切换到设备标签: ${tab.name}`)
      this.activeDeviceTab = tab.name
    },

    // 获取设备名称和状态
    getDeviceNameWithStatus(deviceId) {
      const device = this.getAllDevices().find(d => d.device_id === deviceId)
      if (device) {
        const statusText = this.getStatusText(device.status)
        const deviceIP = this.getDeviceIP(device)
        return `${device.device_name} (${statusText}) - ${deviceIP}`
      }
      return deviceId
    },

    // 获取设备IP地址
    getDeviceIP(device) {
      // 尝试多个可能的IP字段
      return device.ip_address ||
             device.deviceIP ||
             device.device_ip ||
             (device.device_info && typeof device.device_info === 'object' && device.device_info.ipAddress) ||
             (device.device_info && typeof device.device_info === 'object' && device.device_info.ip) ||
             '未知IP'
    },

    // 获取设备标签类型
    getDeviceTagType(deviceId) {
      const device = this.getAllDevices().find(d => d.device_id === deviceId)
      if (device) {
        return this.getStatusTagType(device.status)
      }
      return 'info'
    },

    // 获取所有设备（包括忙碌状态）
    getAllDevices() {
      return this.$store.getters['device/devices']
    },

    // 检查设备是否正在执行当前功能
    isDeviceExecutingCurrentFunction(deviceId) {
      if (!this.selectedFunction) return false
      
      // 从Vuex store获取该设备的运行状态
      const deviceTasks = this.$store.getters['xiaohongshu/getDeviceTasks'](deviceId)
      if (!deviceTasks || deviceTasks.length === 0) return false
      
      // 检查是否有当前功能的运行任务
      return deviceTasks.some(task => task.functionType === this.selectedFunction && task.status === 'running')
    },

    // 自动恢复正在执行当前功能的设备
    autoRestoreExecutingDevices() {
      if (!this.selectedFunction) return
      
      console.log('[小红书自动化] 检查是否有正在执行当前功能的设备')
      
      // 获取所有设备
      const allDevices = this.$store.getters['device/devices']
      
      console.log('[小红书自动化] 设备列表:', allDevices)
      
      // 查找正在执行当前功能的设备
      const executingDevices = allDevices.filter(device => {
        const isBusy = device.status === 'busy'
        const isExecutingCurrentFunction = this.isDeviceExecutingCurrentFunction(device.device_id)
        
        console.log(`[小红书自动化] 设备 ${device.device_name} 检查:`, {
          status: device.status,
          isBusy,
          isExecutingCurrentFunction,
          deviceId: device.device_id
        })
        
        return isBusy && isExecutingCurrentFunction
      })
      
      if (executingDevices.length > 0) {
        console.log('[小红书自动化] 发现正在执行当前功能的设备:', executingDevices)
        
        // 自动选择这些设备
        executingDevices.forEach(device => {
          if (!this.selectedDevices.includes(device.device_id)) {
            this.selectedDevices.push(device.device_id)
            
            // 为新选中的设备初始化配置
            this.initDeviceConfig(device.device_id)
            
            console.log('[小红书自动化] 自动选择设备:', device.device_name)
          }
        })
        
        // 设置活跃标签
        if (this.selectedDevices.length > 0 && !this.activeDeviceTab) {
          this.activeDeviceTab = this.selectedDevices[0]
        }
        
        this.addLog('系统', `自动恢复了 ${executingDevices.length} 个正在执行当前功能的设备`, 'info')
      } else {
        console.log('[小红书自动化] 没有发现正在执行当前功能的设备')
      }
    },

    // 获取在线设备列表
    getOnlineDevices() {
      return this.$store.getters['device/devices'].filter(device =>
        device.status === 'online' || device.status === 'busy'
      )
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 'online':
          return 'success'  // 绿色
        case 'busy':
          return 'warning'  // 橙色
        case 'offline':
          return 'danger'   // 红色
        default:
          return 'info'     // 灰色
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'online':
          return '在线'
        case 'busy':
          return '忙碌'
        case 'offline':
          return '离线'
        default:
          return '未知'
      }
    },

    // ===== 状态管理方法 =====

    // 保存页面状态
    async savePageState() {
      console.log('[状态管理] 保存页面状态')
      try {
        await this.$store.dispatch('xiaohongshu/setPageState', {
          selectedFunction: this.selectedFunction,
          selectedDevices: this.selectedDevices
        })
        console.log('[状态管理] 页面状态已保存:', {
          selectedFunction: this.selectedFunction,
          selectedDevices: this.selectedDevices
        })
      } catch (error) {
        console.error('[状态管理] 保存页面状态失败:', error)
      }
    },

    // 恢复页面状态
    async restorePageState() {
      console.log('[状态管理] 恢复页面状态')
      try {
        const pageState = this.$store.getters['xiaohongshu/getPageState']
        console.log('[状态管理] 获取到的页面状态:', pageState)

        if (pageState.selectedFunction) {
          this.selectedFunction = pageState.selectedFunction
          console.log('[状态管理] 恢复选中功能:', this.selectedFunction)
        }

        if (pageState.selectedDevices && pageState.selectedDevices.length > 0) {
          this.selectedDevices = [...pageState.selectedDevices]
          console.log('[状态管理] 恢复选中设备:', this.selectedDevices)
        }
      } catch (error) {
        console.error('[状态管理] 恢复页面状态失败:', error)
      }
    },

    // 检查并恢复运行中的任务
    async checkAndRestoreRunningTasks() {
      console.log('[状态管理] 检查并恢复运行中的任务')
      try {
        // 调用Vuex action检查运行中的任务
        await this.$store.dispatch('xiaohongshu/checkAndRestoreRunningTasks')

        // 检查各功能的运行状态
        const functions = ['profile', 'searchGroupChat', 'groupMessage', 'articleComment']
        let hasRunningTasks = false

        for (const functionType of functions) {
          const functionState = this.$store.getters['xiaohongshu/getFunctionState'](functionType)

          if (functionState.isScriptRunning) {
            console.log(`[状态管理] 发现运行中的任务: ${functionType}`, functionState)
            hasRunningTasks = true

            // 恢复页面状态
            this.selectedFunction = functionType

            // 恢复选中设备
            if (functionState.selectedDevices && functionState.selectedDevices.length > 0) {
              this.selectedDevices = [...functionState.selectedDevices]
              console.log(`[状态管理] 恢复选中设备:`, this.selectedDevices)

              // 恢复设备配置
              functionState.selectedDevices.forEach(deviceId => {
                if (functionState.config && Object.keys(functionState.config).length > 0) {
                  this.$set(this.deviceConfigs, deviceId, { ...functionState.config })
                  console.log(`[状态管理] 恢复设备 ${deviceId} 的配置:`, functionState.config)
                } else {
                  // 如果没有配置，初始化默认配置
                  this.initDeviceConfig(deviceId)
                }
              })

              // 设置活跃标签页
              if (this.selectedDevices.length > 0) {
                this.activeDeviceTab = this.selectedDevices[0]
              }
            }

            // 发送任务恢复事件到对应组件
            this.$root.$emit('xiaohongshu-task-restored', {
              functionType,
              state: functionState
            })

            this.addLog('系统', `恢复运行中的任务: ${functionType}`, 'info')
          }
        }

        // 检查全局运行状态
        const isAnyTaskRunning = this.$store.getters['xiaohongshu/isAnyTaskRunning']
        if (isAnyTaskRunning) {
          const runningTasks = this.$store.getters['xiaohongshu/getRunningTasks']
          console.log('[状态管理] 发现运行中的任务:', runningTasks)
          this.addLog('系统', `发现 ${runningTasks.length} 个运行中的任务`, 'warning')
        }

        if (hasRunningTasks) {
          console.log('[状态管理] 状态恢复完成')
          console.log('恢复后的状态:')
          console.log('- selectedFunction:', this.selectedFunction)
          console.log('- selectedDevices:', this.selectedDevices)
          console.log('- deviceConfigs:', this.deviceConfigs)
          console.log('- activeDeviceTab:', this.activeDeviceTab)
        }

      } catch (error) {
        console.error('[状态管理] 检查运行中任务失败:', error)
      }
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [XiaohongshuAutomation] 收到实时状态更新:', data)

      // 实时状态更新会被各个子组件自己处理
      // 这里只做日志记录，不需要额外处理
      if (data.deviceId && data.taskId) {
        console.log(`📱 [XiaohongshuAutomation] 设备 ${data.deviceId} 任务 ${data.taskId} 状态更新:`, {
          currentStatus: data.currentStatus,
          operationCount: data.operationCount,
          processedStepCount: data.processedStepCount,
          message: data.message
        })
      } else {
        console.warn('⚠️ [XiaohongshuAutomation] 实时状态数据缺少deviceId或taskId:', data)
      }
    },



    // 处理主页面的实时状态更新
    handleMainRealtimeStatus(data) {
      console.log('🔄 [XiaohongshuAutomation] 收到主页面实时状态更新:', data)
      console.log('🔄 [XiaohongshuAutomation] 当前时间:', new Date().toISOString())
      console.log('🔄 [XiaohongshuAutomation] 数据详情:', JSON.stringify(data, null, 2))

      if (!data.deviceId || !data.taskId) {
        console.warn('⚠️ [XiaohongshuAutomation] 实时状态数据缺少必要字段')
        return
      }

      // 显示实时状态面板
      if (!this.showRealtimeStatus) {
        this.showRealtimeStatus = true
        this.currentExecutionStatus = '执行中'
      }

      // 转发给子组件处理（通过全局事件）
      this.$root.$emit('xiaohongshu_realtime_status', data)
      console.log('📡 [XiaohongshuAutomation] 已转发实时状态给子组件:', data.deviceId)

      // 更新或添加设备到执行列表
      let deviceIndex = this.executingDevices.findIndex(d => d.deviceId === data.deviceId)

      if (deviceIndex === -1) {
        // 新设备，添加到列表
        this.executingDevices.push({
          deviceId: data.deviceId,
          deviceName: this.getDeviceName(data.deviceId),
          status: 'executing',
          realtimeStatus: {
            currentStep: data.currentStep || '执行中',
            currentStatus: data.currentStatus || '进行中',
            message: data.message || '',
            errorMessage: data.errorMessage || '',
            processedCount: data.processedCount || 0,
            totalCount: data.totalCount || 0,
            timestamp: data.timestamp
          }
        })
      } else {
        // 更新现有设备状态
        const device = this.executingDevices[deviceIndex]
        device.realtimeStatus = {
          currentStep: data.currentStep || device.realtimeStatus.currentStep,
          currentStatus: data.currentStatus || device.realtimeStatus.currentStatus,
          message: data.message || device.realtimeStatus.message,
          errorMessage: data.errorMessage || device.realtimeStatus.errorMessage,
          processedCount: data.processedCount || device.realtimeStatus.processedCount,
          totalCount: data.totalCount || device.realtimeStatus.totalCount,
          timestamp: data.timestamp
        }

        // 根据状态更新设备状态
        if (data.currentStatus && data.currentStatus.includes('完成')) {
          device.status = 'completed'
        } else if (data.currentStatus && data.currentStatus.includes('错误')) {
          device.status = 'error'
        } else {
          device.status = 'executing'
        }
      }

      console.log('📊 [XiaohongshuAutomation] 实时状态已更新:', {
        deviceId: data.deviceId,
        currentStep: data.currentStep,
        currentStatus: data.currentStatus
      })
    },

    // 隐藏实时状态面板
    hideRealtimeStatus() {
      this.showRealtimeStatus = false
      this.executingDevices = []
      this.currentExecutionStatus = '等待开始'
    },

    // 🔥 新增：处理脚本重置事件
    handleScriptReset(data) {
      console.log('🔄 [XiaohongshuAutomation] 收到脚本重置事件:', data)

      if (!data.deviceId || !data.functionType) {
        console.warn('⚠️ [XiaohongshuAutomation] 脚本重置事件缺少必要字段')
        return
      }

      // 通知对应的功能组件重置状态
      this.$root.$emit('xiaohongshu-script-reset', {
        functionType: data.functionType,
        deviceId: data.deviceId,
        taskId: data.taskId,
        reason: data.reason || '脚本已停止',
        timestamp: data.timestamp
      })

      // 从执行设备列表中移除该设备
      const deviceIndex = this.executingDevices.findIndex(device => device.deviceId === data.deviceId)
      if (deviceIndex !== -1) {
        this.executingDevices.splice(deviceIndex, 1)
        console.log(`✅ [XiaohongshuAutomation] 已从执行列表中移除设备: ${data.deviceId}`)
      }

      // 如果没有设备在执行，隐藏实时状态面板
      if (this.executingDevices.length === 0) {
        this.hideRealtimeStatus()
        console.log('✅ [XiaohongshuAutomation] 所有设备已停止，隐藏实时状态面板')
      }

      // 重置执行状态
      this.executing = false
      this.currentExecutionStatus = '等待开始'

      console.log(`✅ [XiaohongshuAutomation] 脚本重置完成: ${data.deviceId}`)
    },

    // 🔥 新增：处理Vuex状态更新事件
    handleVuexStateUpdate(data) {
      console.log('🔄 [XiaohongshuAutomation] 收到Vuex状态更新事件:', data)

      if (data.action === 'stopTask' && data.functionType && data.deviceId) {
        // 停止指定设备的任务
        this.$store.dispatch('xiaohongshu/stopTask', {
          functionType: data.functionType,
          deviceId: data.deviceId,
          taskId: data.taskId,
          reason: data.reason
        }).then(() => {
          console.log(`✅ [XiaohongshuAutomation] Vuex状态已更新: ${data.functionType} - ${data.deviceId}`)
        }).catch(error => {
          console.error('❌ [XiaohongshuAutomation] Vuex状态更新失败:', error)
        })
      }
    },

    // 🔥 新增：处理强制刷新Vuex状态事件
    handleForceRefreshVuex(data) {
      console.log('🔄 [XiaohongshuAutomation] 收到强制刷新Vuex状态事件:', data)

      if (data.action === 'batchStop' && data.functionType) {
        // 强制重置指定功能的状态
        this.$store.dispatch('xiaohongshu/resetFunctionState', data.functionType)
          .then(() => {
            console.log(`✅ [XiaohongshuAutomation] 已强制重置 ${data.functionType} 功能状态`)

            // 同时刷新执行状态检查
            return this.$store.dispatch('xiaohongshu/checkRunningTasks')
          })
          .then(() => {
            console.log(`✅ [XiaohongshuAutomation] 已刷新运行中任务状态`)
          })
          .catch(error => {
            console.error('❌ [XiaohongshuAutomation] 强制刷新Vuex状态失败:', error)
          })
      }
    },

    // 🔥 新增：手动刷新Vuex状态
    async refreshVuexState() {
      console.log('🔄 [XiaohongshuAutomation] 手动刷新Vuex状态')

      try {
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在刷新状态...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 1. 强制重置所有功能状态
        await this.$store.dispatch('xiaohongshu/forceResetAllFunctionStates')

        // 2. 重新检查运行中的任务
        await this.$store.dispatch('xiaohongshu/checkRunningTasks')

        // 3. 刷新设备列表
        await this.loadDevices()

        // 4. 重新检查和恢复运行中的任务
        await this.checkAndRestoreRunningTasks()

        loading.close()

        this.$message.success('状态刷新完成！')
        console.log('✅ [XiaohongshuAutomation] Vuex状态手动刷新完成')

      } catch (error) {
        console.error('❌ [XiaohongshuAutomation] 手动刷新Vuex状态失败:', error)
        this.$message.error('状态刷新失败: ' + error.message)
      }
    },

    // 获取当前执行功能名称
    getCurrentFunctionName() {
      const currentFunction = this.functions.find(f => f.key === this.selectedFunction)
      return currentFunction ? currentFunction.name : '未知功能'
    },

    // 获取状态标签类型
    getStatusTagType() {
      if (this.currentExecutionStatus.includes('完成')) {
        return 'success'
      } else if (this.currentExecutionStatus.includes('错误') || this.currentExecutionStatus.includes('失败')) {
        return 'danger'
      } else if (this.currentExecutionStatus.includes('执行')) {
        return 'warning'
      } else {
        return 'info'
      }
    },

    // 获取设备状态类型
    getDeviceStatusType(status) {
      switch (status) {
        case 'completed':
          return 'success'
        case 'error':
          return 'danger'
        case 'executing':
          return 'warning'
        default:
          return 'info'
      }
    },

    // 获取状态样式类
    getStatusClass(status) {
      if (status && status.includes('错误')) {
        return 'status-error'
      } else if (status && status.includes('完成')) {
        return 'status-success'
      } else if (status && status.includes('警告')) {
        return 'status-warning'
      } else {
        return 'status-normal'
      }
    },

    // 检查是否有进度信息
    hasProgressInfo(realtimeStatus) {
      return realtimeStatus &&
             (realtimeStatus.processedCount !== undefined || realtimeStatus.totalCount !== undefined) &&
             realtimeStatus.totalCount > 0
    },

    // 获取设备进度百分比
    getDeviceProgress(realtimeStatus) {
      if (!this.hasProgressInfo(realtimeStatus)) return 0
      const processed = realtimeStatus.processedCount || 0
      const total = realtimeStatus.totalCount || 1
      return Math.round((processed / total) * 100)
    },

    // 获取进度条状态
    getProgressStatus(realtimeStatus) {
      if (realtimeStatus && realtimeStatus.errorMessage) {
        return 'exception'
      } else if (this.getDeviceProgress(realtimeStatus) === 100) {
        return 'success'
      } else {
        return null
      }
    }
  }
}
</script>

<style scoped>
.xiaohongshu-automation {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content > div:first-child {
  text-align: left;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #909399;
  font-size: 14px;
}

.header-actions {
  flex-shrink: 0;
}

.function-cards {
  margin-bottom: 30px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 140px;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.function-card.active {
  border-color: #409EFF;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.card-content {
  text-align: center;
  padding: 10px;
  position: relative;
}

.card-header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 10px;
}

.function-icon {
  font-size: 32px;
  color: #409EFF;
}

.batch-stop-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  min-width: 24px;
  height: 24px;
  padding: 0;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.batch-stop-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.card-content h3 {
  margin: 10px 0 5px 0;
  color: #303133;
  font-size: 16px;
}

.card-content p {
  color: #909399;
  font-size: 12px;
  margin: 0;
}

.running-status {
  margin-top: 8px;
}

.running-indicator {
  display: inline-block;
  background: linear-gradient(45deg, #67C23A, #85CE61);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.config-panel, .log-panel {
  margin-bottom: 20px;
}

.config-section {
  margin-bottom: 25px;
}

.config-section h4 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: 600;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.log-time {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.log-device {
  color: #606266;
  width: 100px;
  flex-shrink: 0;
}

.log-message {
  color: #303133;
  flex: 1;
}

.log-item.error .log-message {
  color: #F56C6C;
}

.log-item.success .log-message {
  color: #67C23A;
}

.log-item.warning .log-message {
  color: #E6A23C;
}

.empty-logs {
  text-align: center;
  color: #909399;
  padding: 40px 0;
}

.no-component {
  margin: 20px 0;
}

/* 多设备配置样式 */
.device-config-tabs {
  margin-top: 20px;
}

.device-config-content {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 4px;
  margin-top: 10px;
}

.device-config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.device-config-header h5 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.batch-execute-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}

.batch-execute-section .el-button {
  margin: 0 10px;
}

/* 设备标签页样式优化 */
.device-config-tabs .el-tabs__header {
  margin-bottom: 0;
}

.device-config-tabs .el-tabs__item {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

.device-config-tabs .el-tabs__content {
  padding: 0;
}

/* 配置区域样式 */
.config-section {
  margin-bottom: 20px;
}

.config-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

/* 实时状态面板样式 */
.realtime-status-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.status-card {
  margin: 0;
}

.status-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.status-content {
  padding: 0;
}

.status-section {
  margin-bottom: 20px;
}

.status-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}

.function-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.function-name {
  font-weight: 600;
  color: #303133;
}

.device-status-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.device-status-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.device-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.realtime-info {
  margin-top: 8px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.status-row.error {
  background: #fef0f0;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
}

.status-row .label {
  color: #909399;
  font-weight: 500;
  min-width: 60px;
}

.status-row .value {
  color: #303133;
  font-weight: 600;
  text-align: right;
  flex: 1;
}

.status-row .value.status-error {
  color: #f56c6c;
}

.status-row .value.status-success {
  color: #67c23a;
}

.status-row .value.status-warning {
  color: #e6a23c;
}

.status-row .value.status-normal {
  color: #409eff;
}

.progress-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e4e7ed;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  text-align: center;
}
</style>
