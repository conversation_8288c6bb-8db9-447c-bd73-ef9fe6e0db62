/**
 * 服务器小红书自动化模块 - 完整拆分版本
 * 包含所有小红书自动化相关的API和功能
 * 对应原始文件第1850-8600行的完整内容，包含以下40个API：
 * - GET /api/xiaohongshu/test - 测试路由
 * - POST /api/xiaohongshu/execute - 执行小红书自动化任务
 * - POST /api/xiaohongshu/status - 接收脚本执行状态更新
 * - POST /api/xiaohongshu/debug-log - 接收脚本调试日志
 * - POST /api/xiaohongshu/upload-uid-file - UID文件上传
 * - GET /api/xiaohongshu/uid-files - 获取UID文件列表
 * - GET /api/xiaohongshu/uid-files/:fileId/uids - 获取指定文件的UID列表
 * - DELETE /api/xiaohongshu/uid-files/:fileId - 删除UID文件
 * - POST /api/xiaohongshu/uid-files/:fileId/reset-status - 重置UID状态
 * - GET /api/xiaohongshu/tasks - 获取小红书任务状态
 * - POST /api/xiaohongshu/stop - 停止小红书脚本执行
 * - POST /api/xiaohongshu/stop-device - 停止特定设备的小红书脚本执行
 * - POST /api/xiaohongshu/stop-all - 停止所有小红书脚本执行
 * - GET /api/xiaohongshu/debug-status - 调试API：查看当前执行状态
 * - POST /api/xiaohongshu/stop-by-function - 按功能类型批量停止小红书脚本执行
 * - GET /api/xiaohongshu/logs - 获取小红书执行日志列表
 * - GET /api/xiaohongshu/logs/:taskId - 获取小红书执行日志详情
 * - DELETE /api/xiaohongshu/logs - 清空小红书执行日志
 * - POST /api/xiaohongshu/upload-uids - 上传UID文件
 * - POST /api/xiaohongshu/record-uid-message - 记录UID私信结果
 * - GET /api/xiaohongshu/uid-status/:uid - 查询UID状态
 * - POST /api/xiaohongshu/execution-complete - 接收脚本执行完成通知
 * - POST /api/xiaohongshu/upload-video-files - 上传视频文件（支持批量上传，带重复检测）
 * - DELETE /api/xiaohongshu/videos/:id - 删除单个视频
 * - GET /api/xiaohongshu/download-video/:id - 下载视频文件API - 支持分块传输和断点续传
 * - POST /api/xiaohongshu/transfer-videos - 传输视频到手机设备（独立功能）
 * - POST /api/xiaohongshu/report-transfer-progress - 上报视频传输进度（实时进度）
 * - GET /api/xiaohongshu/transfer-records - 查询视频传输记录
 * - POST /api/xiaohongshu/update-transfer-status - 更新视频传输状态
 * - GET /api/xiaohongshu/download-video/:videoId - 视频下载API - 供手机端下载视频文件，支持分块传输
 * - POST /api/xiaohongshu/realtime-status - 小红书实时状态API（统一处理所有功能）
 * - POST /api/xiaohongshu/smart-select-videos - 智能视频选择API
 * - GET /api/xiaohongshu/video-files - 获取已上传的视频文件列表
 * - GET /api/xiaohongshu/video-stats - 获取视频文件统计信息
 * - DELETE /api/xiaohongshu/video-files/:videoId - 删除视频文件
 * - POST /api/xiaohongshu/assign-videos - 分配视频给设备
 * - GET /api/xiaohongshu/device-video-assignments/:deviceId - 获取设备的视频分配情况
 * - POST /api/xiaohongshu/video-publish-progress - 视频发布进度上报
 * - POST /api/xiaohongshu/video-publish-result - 视频发布结果上报
 * - POST /api/xiaohongshu/stop-device-tasks - 停止设备任务API
 * 以及所有相关的脚本转换函数和工具函数
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');

// 引入用户隔离中间件和工具
const { userIsolationMiddleware } = require('../middleware/userIsolation');
const DatabaseQueryEnhancer = require('../utils/DatabaseQueryEnhancer');
const PermissionValidator = require('../utils/PermissionValidator');

// 通用的服务器地址替换函数
function replaceServerAddresses(script, req) {
  // 获取真实的服务器地址（手机端可以访问的地址）
  let serverHost = req.get('host') || 'localhost:3002';

  // 如果是localhost，尝试获取PC的真实IP地址
  if (serverHost.includes('localhost') || serverHost.includes('127.0.0.1')) {
      const os = require('os');
      const networkInterfaces = os.networkInterfaces();

      // 查找第一个非回环的IPv4地址
      for (const interfaceName in networkInterfaces) {
          const addresses = networkInterfaces[interfaceName];
          for (const addr of addresses) {
              if (addr.family === 'IPv4' && !addr.internal) {
                  serverHost = `${addr.address}:3002`;
                  break;
              }
          }
          if (!serverHost.includes('localhost')) break;
      }
  }

  // 替换所有可能的服务器地址格式
  script = script.replace(/http:\/\/192\.168\.1\.91:3002/g, `http://${serverHost}`);
  script = script.replace(/192\.168\.1\.91:3002/g, serverHost);
  script = script.replace(/http:\/\/localhost:3002/g, `http://${serverHost}`);
  script = script.replace(/localhost:3002/g, serverHost);
  script = script.replace(/127\.0\.0\.1:3002/g, serverHost);

  console.log(`脚本地址替换完成，目标地址: ${serverHost}`);
  return script;
}

// 构建功能特定的配置参数
function buildXiaohongshuFunctionConfig(functionType, config) {
  switch (functionType) {
    case 'profile':
      return {
        nickname: config.nickname || '',
        profile: config.profile || '',
        onlyNickname: config.modifyOptions?.includes('onlyNickname') || false,
        onlyProfile: config.modifyOptions?.includes('onlyProfile') || false,
        autoSave: config.modifyOptions?.includes('autoSave') || false,
        operationDelay: config.operationDelay || 2,
        safetyOptions: config.safetyOptions || [],
        // 安全设置的具体选项
        backupOriginal: config.safetyOptions?.includes('backupOriginal') || false,
        confirmBeforeChange: config.safetyOptions?.includes('confirmBeforeChange') || false,
        validateInput: config.safetyOptions?.includes('validateInput') || false,
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'searchGroupChat':
      return {
        searchKeyword: config.searchKeyword || '私域',
        targetJoinCount: config.targetJoinCount || 5,
        maxScrollAttempts: config.maxScrollAttempts || 10,
        enableDetailedLog: config.enableDetailedLog || false,
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'groupChat':
      return {
        searchKeyword: config.searchKeyword || '私域',
        targetJoinCount: config.targetJoinCount || 5,
        operationInterval: config.operationInterval || 10,
        joinMessage: config.joinMessage || '',
        sendMessage: config.joinSettings?.includes('sendMessage') || false,
        autoScroll: config.joinSettings?.includes('autoScroll') || true
      };

    case 'groupMessage':
      return {
        messageContent: config.messageContent || '',
        sendInterval: config.sendInterval || 10,
        executionMode: config.executionMode || 'once',
        loopInterval: config.loopInterval || 60,
        randomDelay: config.sendSettings?.includes('randomDelay') || true,
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'articleComment':
      return {
        searchKeyword: config.searchKeyword || '美食推荐',
        commentCount: config.commentCount || 3,
        operationDelay: config.operationDelay || 5,
        commentType: config.commentType || 'template',
        customComments: config.customComments || '',
        commentTemplates: config.commentTemplates || ['praise'],
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'uidMessage':
      return {
        inputMode: config.inputMode || 'manual',
        uidList: config.uidList || [],
        uidStrategy: config.uidStrategy || 'sequential',
        message: config.message || '',
        delay: config.delay || 5,
        maxCount: config.maxCount || 10,
        enableDetailLog: config.enableDetailLog !== undefined ? config.enableDetailLog : true,
        skipUsedUids: config.skipUsedUids !== undefined ? config.skipUsedUids : true,
        autoMarkUsed: config.autoMarkUsed !== undefined ? config.autoMarkUsed : true,
        taskId: config.taskId || '',
        deviceId: config.deviceId || '',
        deviceName: config.deviceName || '',
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'uidFileMessage':
      return {
        inputMode: 'file', // 文件上传模式固定为file
        uidList: config.uidList || [],
        selectedFileId: config.selectedFileId || null,
        fileId: config.selectedFileId || null, // 添加fileId参数供脚本使用
        uidsPerDevice: config.uidsPerDevice || 5,
        totalUidCount: config.totalUidCount || 10,
        uidStrategy: config.uidStrategy || 'sequential',
        message: config.message || '',
        delay: config.delay || 5,
        maxCount: config.maxCount || 10,
        enableDetailLog: config.enableDetailLog !== undefined ? config.enableDetailLog : true,
        skipUsedUids: config.skipUsedUids !== undefined ? config.skipUsedUids : true,
        autoMarkUsed: config.autoMarkUsed !== undefined ? config.autoMarkUsed : true,
        taskId: config.taskId || '',
        deviceId: config.deviceId || '',
        deviceName: config.deviceName || '',
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'videoPublish':
      return {
        selectedApp: config.selectedApp || '',
        titleTemplate: config.titleTemplate || '{filename}',
        videoDescription: config.videoDescription || '',
        hashtags: config.hashtags || '',
        publishOptions: config.publishOptions || ['allowComment', 'allowShare'],
        operationDelay: config.operationDelay || 5,
        retryCount: config.retryCount || 2,
        selectedVideoIds: config.selectedVideoIds || [],
        selectedVideos: config.selectedVideos || [],
        videoCount: config.videoCount || 1,
        publishInterval: config.publishInterval || 30,
        publishTime: config.publishTime || 'immediate'
      };

    default:
      return config;
  }
}

// 获取功能名称
function getXiaohongshuFunctionName(functionType) {
  const nameMap = {
    'profile': '修改资料',
    'groupChat': '搜索加群',
    'searchGroupChat': '搜索加群',  // 添加搜索加群功能映射
    'groupMessage': '循环群发',
    'articleComment': '文章评论',
    'uidMessage': 'UID私信',
    'uidFileMessage': 'UID文件私信',
    'videoPublish': '视频发布'
  };
  return nameMap[functionType] || functionType;
}

// 记录脚本执行时的视频传输（与原始服务器完全一致）
async function recordVideoTransfersForScript(selectedVideos, deviceId, taskId, status = 'completed', pool) {
  try {
    console.log('📝📝📝 [脚本传输记录] ===== 开始记录脚本执行的视频传输 ===== 📝📝📝');
    console.log('📝 [脚本传输记录] 函数被调用时间:', new Date().toLocaleString());
    console.log('📝 [脚本传输记录] 设备ID:', deviceId);
    console.log('📝 [脚本传输记录] 任务ID:', taskId);
    console.log('📝 [脚本传输记录] 传输状态:', status);
    console.log('📝 [脚本传输记录] 选中的视频:', selectedVideos);
    console.log('📝 [脚本传输记录] 视频数量:', selectedVideos ? selectedVideos.length : 0);

    if (!selectedVideos || selectedVideos.length === 0) {
      console.log('📝 [脚本传输记录] 没有选中的视频，跳过记录');
      return;
    }

    if (!pool) {
      console.log('❌ [脚本传输记录] 数据库连接不可用');
      return;
    }

    // 检查是否已存在相同任务ID的记录，避免重复创建
    console.log('🔍 [脚本传输记录] 检查是否已存在相同任务ID的记录...');
    const [existingRecords] = await pool.execute(`
      SELECT COUNT(*) as count FROM xiaohongshu_video_transfers
      WHERE task_id = ? AND device_id = ?
    `, [taskId, deviceId]);

    if (existingRecords[0].count > 0) {
      console.log('⚠️ [脚本传输记录] 任务ID已存在记录，跳过重复创建:', taskId);
      return;
    }

    // 获取设备信息
    let deviceName = deviceId;
    try {
      const [devices] = await pool.execute(`
        SELECT device_name FROM devices WHERE device_id = ?
      `, [deviceId]);

      if (devices.length > 0) {
        deviceName = devices[0].device_name;
      }
    } catch (error) {
      console.log('⚠️ [脚本传输记录] 获取设备名称失败，使用设备ID:', error.message);
    }

    // 为每个视频创建传输记录
    for (const video of selectedVideos) {
      try {
        console.log(`📝 [脚本传输记录] 记录视频 ${video.id} (${video.original_name}) 传输到设备 ${deviceId}`);

        const completedTime = status === 'completed' ? 'NOW()' : 'NULL';

        await pool.execute(`
          INSERT INTO xiaohongshu_video_transfers
          (video_id, device_id, device_name, transfer_type, task_id, status, file_size, video_filename, transfer_time, completed_time)
          VALUES (?, ?, ?, 'script_execution', ?, ?, ?, ?, NOW(), ${completedTime})
        `, [video.id, deviceId, deviceName, taskId, status, video.file_size || 0, video.original_name || '']);

        console.log(`✅ [脚本传输记录] 已记录视频 ${video.id} 传输到设备 ${deviceId}`);
      } catch (error) {
        console.error(`❌ [脚本传输记录] 记录视频 ${video.id} 传输失败:`, error.message);
      }
    }

    console.log('✅ [脚本传输记录] 所有视频传输记录完成');
  } catch (error) {
    console.error('❌ [脚本传输记录] 记录视频传输失败:', error);
  }
}

// 小红书自动化模块设置函数
async function setupServerXiaohongshu(app, io, coreData, authData, utilsFunctions) {
  console.log('🔧 设置小红书自动化模块...');

  const {
    pool,
    devices,
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    xiaohongshuLogService,
    upload,
    videoUpload,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 从工具函数模块中提取updateDeviceStatus函数
  const { updateDeviceStatus } = utilsFunctions;

  // 引入设备认证中间件
  const { authenticateDevice, verifyDeviceOwnership } = require('../middleware/deviceAuth');

  // 用户隔离的WebSocket广播函数
  function broadcastToUserClients(userId, event, data) {
    let sentCount = 0;

    // 将userId转换为字符串进行比较
    const targetUserId = String(userId);

    console.log(`📡 [用户隔离广播] 开始广播事件${event}给用户${targetUserId}`);
    console.log(`📡 [用户隔离广播] 当前连接的客户端数量: ${webClients.size}`);

    // 遍历所有Web客户端连接
    for (const [clientSocketId, clientData] of webClients) {
      console.log(`📡 [用户隔离广播] 检查客户端 ${clientSocketId}: userId=${clientData.userId}, clientType=${clientData.clientType}`);

      // 检查客户端是否属于指定用户（支持多种格式）
      const clientUserId = String(clientData.userId);
      const isTargetUser = (
        clientUserId === targetUserId ||                    // 直接匹配
        clientUserId === `user_${targetUserId}` ||          // user_前缀格式
        clientData.userId === parseInt(targetUserId) ||     // 数字格式
        (clientData.username && clientData.username === `user_${targetUserId}`) // username格式
      );

      if (isTargetUser) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket) {
          clientSocket.emit(event, data);
          sentCount++;
          console.log(`📡 [用户隔离广播] ✅ 已发送给客户端 ${clientSocketId} (userId: ${clientData.userId})`);
        } else {
          console.log(`📡 [用户隔离广播] ❌ 客户端Socket不存在: ${clientSocketId}`);
        }
      }
    }

    console.log(`📡 [用户隔离广播] 事件${event}已发送给用户${targetUserId}的${sentCount}个客户端`);
    return sentCount;
  }

  // 创建全局的数据库查询增强器和权限验证器
  const dbEnhancer = new DatabaseQueryEnhancer(pool);
  const permissionValidator = new PermissionValidator(pool);

  // 小红书自动化任务存储
  let xiaohongshuActiveTasks = new Map();
  let xiaohongshuTaskHistory = [];

  // 内存存储UID数据（测试用）
  const uidStorage = new Map(); // 存储UID文件数据
  const uidMessageStorage = new Map(); // 存储UID私信记录

  // 视频文件存储
  const videoStorage = new Map(); // 存储视频文件信息
  const videoTransferRecords = new Map(); // 存储视频传输记录

  // 测试路由 (原始文件第1850行)
  app.get('/api/xiaohongshu/test', (req, res) => {
    res.json({
      success: true,
      message: '小红书自动化API正常工作',
      timestamp: new Date().toISOString(),
      routes: [
        'GET /api/xiaohongshu/test',
        'POST /api/xiaohongshu/execute',
        'POST /api/xiaohongshu/stop',
        'GET /api/xiaohongshu/tasks',
        'GET /api/xiaohongshu/logs',
        'POST /api/xiaohongshu/upload-uid-file',
        'GET /api/xiaohongshu/uid-files',
        'POST /api/xiaohongshu/upload-video-files',
        'GET /api/xiaohongshu/video-files'
      ]
    });
  });

  // 执行小红书自动化任务API (原始文件第1865行) - 已添加用户隔离
  app.post('/api/xiaohongshu/execute', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { functionType, config, deviceConfigs, schedule, deviceIds, taskId } = req.body;
      const userId = req.currentUserId;

      const requestTime = new Date().toLocaleTimeString();
      console.log(`🔍 [${requestTime}] 用户${userId}发起小红书自动化执行请求:`);
      console.log('- 功能类型:', functionType);
      console.log('- 设备数量:', deviceIds ? deviceIds.length : 0);
      console.log('- 调度模式:', schedule ? schedule.mode : 'unknown');
      console.log('- 请求来源IP:', req.ip || req.connection.remoteAddress);
      console.log('- User-Agent:', req.get('User-Agent') ? req.get('User-Agent').substring(0, 50) + '...' : 'unknown');
      console.log('- 原始配置 config:', JSON.stringify(config, null, 2));
      console.log('- 设备独立配置 deviceConfigs:', JSON.stringify(deviceConfigs, null, 2));
      console.log('- 完整请求体:', JSON.stringify(req.body, null, 2));

      // 特别检查groupChat功能的关键词
      if (functionType === 'groupChat') {
        console.log('🔍 群聊功能特别检查:');
        console.log('- 用户输入的搜索关键词:', config.searchKeyword);
        console.log('- 目标加入次数:', config.targetJoinCount);
        console.log('- 操作间隔:', config.operationInterval);
        console.log('- config对象的所有属性:', Object.keys(config));
        console.log('- config对象是否为空:', Object.keys(config).length === 0);

        // 如果config为空或缺少关键字段，记录警告
        if (!config.searchKeyword) {
          console.log('⚠️ 警告: searchKeyword字段缺失或为空!');
          console.log('- config.searchKeyword值:', config.searchKeyword);
          console.log('- config.searchKeyword类型:', typeof config.searchKeyword);
        }
      }

      // 验证参数
      if (!functionType || !deviceIds || deviceIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 验证设备所属权
      console.log(`[小红书执行] 验证用户${userId}对设备的权限: ${deviceIds.join(', ')}`);
      const devicePermissions = await permissionValidator.validateBatchOwnership('device', deviceIds, userId);

      if (!devicePermissions.hasPermission) {
        console.log(`[小红书执行] 用户${userId}无权访问设备: ${devicePermissions.invalid.join(', ')}`);
        return res.status(403).json({
          success: false,
          message: `无权访问以下设备: ${devicePermissions.invalid.join(', ')}`
        });
      }

      // 验证设备独立配置
      if (!deviceConfigs || typeof deviceConfigs !== 'object') {
        console.log('⚠️ 没有设备独立配置，使用通用配置作为后备');
      } else {
        console.log('✅ 检测到设备独立配置，设备数量:', Object.keys(deviceConfigs).length);
        for (const [deviceId, deviceConfig] of Object.entries(deviceConfigs)) {
          console.log(`- 设备 ${deviceId} 配置:`, JSON.stringify(deviceConfig, null, 2));
        }
      }

      // 根据功能类型选择对应的脚本文件
      const scriptMap = {
        'profile': './jb/无ui界面6.30.js',
        'searchGroupChat': './jb/无ui界面 群聊.js',
        'groupChat': './jb/群聊.js',
        'groupMessage': './jb/每小时群发消息-非UI版本.js',
        'articleComment': './jb/search_comment_no_ui.js',
        'uidMessage': './jb/小红书UID私信脚本-无UI版.js',
        'uidFileMessage': './jb/小红书UID私信脚本-无UI版.js',
        // 添加发布视频功能 - 支持两种命名方式（使用测试版脚本）
        'publishVideo': './jb/小红书发布视频脚本-测试版.js',
        'videoPublish': './jb/小红书发布视频脚本-测试版.js',
        // 添加简化测试脚本
        'videoTest': './jb/测试脚本-简化版.js',
        // 添加视频传输测试脚本
        'videoTransfer': './jb/视频传输测试脚本.js'
      };

      console.log('🔍 脚本映射检查:');
      console.log('- 请求的功能类型:', functionType);
      console.log('- 可用的功能类型:', Object.keys(scriptMap));
      console.log('- 映射的脚本路径:', scriptMap[functionType]);

      if (!scriptMap[functionType]) {
        return res.status(400).json({
          success: false,
          message: `不支持的功能类型: ${functionType}。支持的功能类型: ${Object.keys(scriptMap).join(', ')}`
        });
      }

      const scriptPath = path.resolve(scriptMap[functionType]);

      console.log('尝试读取脚本文件:', scriptPath);
      console.log('文件是否存在:', fs.existsSync(scriptPath));

      if (!fs.existsSync(scriptPath)) {
        return res.status(404).json({
          success: false,
          message: `脚本文件不存在: ${scriptPath}`
        });
      }

      // 读取脚本内容
      let scriptContent;
      try {
        scriptContent = fs.readFileSync(scriptPath, 'utf8');
        console.log('✅ 脚本文件读取成功，长度:', scriptContent.length);
      } catch (error) {
        console.error('❌ 读取脚本文件失败:', error);
        return res.status(500).json({
          success: false,
          message: `读取脚本文件失败: ${error.message}`
        });
      }

      // 创建任务记录
      const task = {
        id: taskId || Date.now().toString(),
        functionType,
        config,
        deviceConfigs,
        deviceIds,
        status: 'pending',
        createdAt: new Date(),
        startedAt: null,
        completedAt: null,
        results: []
      };

      xiaohongshuActiveTasks.set(task.id, task);

      // 发送脚本到设备
      let successCount = 0;
      let failCount = 0;
      const results = [];

      for (const deviceId of deviceIds) {
        try {
          // 获取设备特定配置或使用通用配置
          const rawDeviceConfig = deviceConfigs && deviceConfigs[deviceId] ? deviceConfigs[deviceId] : config;

          // 使用参数处理函数构建标准化配置
          const deviceConfig = buildXiaohongshuFunctionConfig(functionType, rawDeviceConfig);

          console.log(`📋 设备 ${deviceId} 参数处理结果:`, JSON.stringify(deviceConfig, null, 2));

          // 生成任务ID（确保包含xiaohongshu前缀以便HTTP结果处理识别）
          // 如果前端传递了taskId，检查是否已经包含设备ID，避免重复拼接
          let generatedTaskId;
          if (taskId) {
            // 检查taskId是否已经包含当前设备ID
            if (taskId.includes(`_${deviceId}`)) {
              generatedTaskId = taskId; // 已经包含设备ID，直接使用
              console.log(`📋 任务ID已包含设备ID，直接使用: ${generatedTaskId}`);
            } else {
              generatedTaskId = `${taskId}_${deviceId}`; // 拼接设备ID
              console.log(`📋 任务ID拼接设备ID: ${generatedTaskId}`);
            }
          } else {
            generatedTaskId = `xiaohongshu_${functionType}_${Date.now()}_${deviceId}`;
            console.log(`📋 生成新任务ID: ${generatedTaskId}`);
          }

          // 创建数据库执行日志记录
          if (xiaohongshuLogService) {
            try {
              // 获取设备名称
              let deviceName = deviceId;
              for (const [socketId, deviceData] of devices) {
                if (deviceData.deviceId === deviceId) {
                  deviceName = deviceData.deviceName || deviceId;
                  break;
                }
              }

              await xiaohongshuLogService.createExecutionLogWithUserId(
                generatedTaskId,
                functionType,
                deviceId,
                deviceName,
                deviceConfig,
                schedule,
                userId
              );
              console.log(`✅ 数据库执行日志已创建: ${generatedTaskId} (用户${userId})`);
            } catch (logError) {
              console.error('创建执行日志失败:', logError);
            }
          }

          console.log(`📱 准备发送脚本到设备 ${deviceId}:`);
          console.log('- 功能类型:', functionType);
        console.log('- 原始配置:', JSON.stringify(config, null, 2));
        console.log('- 设备配置:', JSON.stringify(deviceConfig, null, 2));
        console.log('- 设备配置中的关键参数:');
        console.log('  - nickname:', deviceConfig.nickname);
        console.log('  - profile:', deviceConfig.profile);
        console.log('  - uidList:', deviceConfig.uidList ? deviceConfig.uidList.length : 'undefined');
        console.log('  - messageContent:', deviceConfig.messageContent);

          // 查找设备
          let device = null;
          for (const [socketId, deviceData] of devices) {
            if (deviceData.deviceId === deviceId) {
              device = deviceData;
              break;
            }
          }

          if (!device) {
            throw new Error(`设备 ${deviceId} 不在线`);
          }

          // 根据功能类型进行脚本转换（原始实现方式）
          let finalScript;

          if (functionType === 'profile') {
            // 修改资料功能：直接使用无UI脚本（参考原始server.js逻辑）
            console.log(`=== 设备 ${deviceId} 修改资料功能：直接使用无UI脚本 ===`);

            // 移除原始的main()调用
            let modifiedScript = scriptContent.replace(/main\(\);?\s*$/m, '');

            // 处理控制参数转换
            const modifyOptions = deviceConfig.modifyOptions || [];
            const onlyNickname = modifyOptions.includes('onlyNickname');
            const onlyProfile = modifyOptions.includes('onlyProfile');

            // 处理安全选项转换
            const safetyOptions = deviceConfig.safetyOptions || [];
            const backupOriginal = safetyOptions.includes('backupOriginal');
            const confirmBeforeChange = safetyOptions.includes('confirmBeforeChange');
            const validateInput = safetyOptions.includes('validateInput');

            console.log(`控制参数转换: modifyOptions=${JSON.stringify(modifyOptions)} -> onlyNickname=${onlyNickname}, onlyProfile=${onlyProfile}`);
            console.log(`安全选项转换: safetyOptions=${JSON.stringify(safetyOptions)} -> backupOriginal=${backupOriginal}, confirmBeforeChange=${confirmBeforeChange}, validateInput=${validateInput}`);

            // 创建包含转换后参数的配置对象
            const processedConfig = {
              ...deviceConfig,
              // 添加转换后的布尔值参数
              backupOriginal: backupOriginal,
              confirmBeforeChange: confirmBeforeChange,
              validateInput: validateInput
            };

            // 在脚本开头注入参数并直接调用executeScript
            const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(processedConfig, null, 2)};

// 控制参数转换
const onlyNickname = ${onlyNickname};
const onlyProfile = ${onlyProfile};

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 直接执行脚本，跳过UI输入
console.log("=== 小红书资料修改脚本启动（无UI模式）===");
console.log("收到服务器参数:", JSON.stringify(serverParams, null, 2));
console.log("安全选项状态: backupOriginal=${backupOriginal}, confirmBeforeChange=${confirmBeforeChange}, validateInput=${validateInput}");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("昵称: " + serverParams.nickname);
console.log("简介: " + serverParams.profile);
console.log("只修改昵称: " + onlyNickname);
console.log("只修改简介: " + onlyProfile);

// 参数验证
if (!serverParams.nickname && !serverParams.profile) {
    console.error("错误: 昵称和简介都为空");
    throw new Error("昵称和简介都为空");
}

// 设置运行状态
isRunning = true;

try {
    executeScript(serverParams.nickname, serverParams.profile, onlyNickname, onlyProfile, serverParams);
    console.log("脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "修改资料脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });
} catch (e) {
    console.error("脚本执行出错: " + e.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "修改资料脚本执行失败: " + e.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (err) {
            console.log("执行失败状态上报失败: " + err.message);
        }
    });
    throw e;
} finally {
    isRunning = false;
    console.log("=== 脚本执行结束 ===");
}

`;
            finalScript = paramInjection + modifiedScript;

            // 替换脚本中的占位符和硬编码地址
            finalScript = finalScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
            finalScript = finalScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

            // 使用通用函数替换服务器地址
            finalScript = replaceServerAddresses(finalScript, req);

          } else if (functionType === 'uidMessage' || functionType === 'uidFileMessage') {
            // UID私信功能：直接使用无UI脚本
            console.log(`=== 设备 ${deviceId} ${functionType === 'uidFileMessage' ? '文件上传' : '手动输入'}UID私信功能：直接使用无UI脚本 ===`);

            // 处理UID获取逻辑
            if (deviceConfig.inputMode === 'file' || functionType === 'uidFileMessage') {
              console.log('文件模式：从数据库获取UID列表');

              try {
                // 使用数据库获取UID
                if (pool && deviceConfig.selectedFileId) {
                  const connection = await pool.getConnection();

                  try {
                    // 获取可用的UID（包含ID用于后续标记）
                    const maxCount = deviceConfig.totalUidCount || 10;
                    const [uidRows] = await connection.execute(
                      'SELECT id, uid FROM uid_data WHERE file_id = ? AND is_used = 0 ORDER BY id ASC LIMIT ?',
                      [deviceConfig.selectedFileId, maxCount]
                    );

                    if (uidRows.length > 0) {
                      // 提取UID列表
                      deviceConfig.uidList = uidRows.map(row => row.uid);
                      deviceConfig.uidIds = uidRows.map(row => row.id); // 保存ID用于后续标记

                      console.log(`从数据库获取到 ${deviceConfig.uidList.length} 个可用UID`);
                      console.log('UID列表:', deviceConfig.uidList);

                      // 预先标记这些UID为当前任务使用（但不标记为已使用）
                      const uidIdList = deviceConfig.uidIds.join(',');
                      await connection.execute(
                        `UPDATE uid_data SET task_id = ? WHERE id IN (${uidIdList})`,
                        [generatedTaskId]
                      );
                      console.log(`已为任务 ${generatedTaskId} 预分配 ${deviceConfig.uidIds.length} 个UID`);

                    } else {
                      console.warn(`文件 ${deviceConfig.selectedFileId} 中没有可用的UID`);
                      deviceConfig.uidList = [];
                    }
                  } finally {
                    connection.release();
                  }
                } else {
                  console.warn('缺少数据库连接或文件ID，无法获取UID列表');
                  deviceConfig.uidList = [];
                }
              } catch (dbError) {
                console.error('从数据库获取UID失败:', dbError);
                deviceConfig.uidList = [];
              }
            } else {
              console.log('手动模式：使用前端传递的UID列表');
              console.log('UID列表:', deviceConfig.uidList);
            }

            // 获取服务器IP地址
            const os = require('os');
            const networkInterfaces = os.networkInterfaces();
            let serverIP = 'localhost';

            for (const interfaceName in networkInterfaces) {
              const iface = networkInterfaces[interfaceName];
              for (const alias of iface) {
                if (alias.family === 'IPv4' && !alias.internal) {
                  serverIP = alias.address;
                  break;
                }
              }
              if (serverIP !== 'localhost') break;
            }

            deviceConfig.serverHost = serverIP + ':3002';
            console.log(`为设备 ${deviceId} 设置服务器地址: ${deviceConfig.serverHost}`);

            // UID私信功能是无UI版本，直接注入参数
            const paramInjection = `
// 服务器传递的UID私信参数
const globalConfig = ${JSON.stringify(deviceConfig, null, 2)};
const webConfig = globalConfig; // 兼容原脚本中的webConfig变量名

`;
            finalScript = paramInjection + scriptContent;

            // 替换脚本中的占位符
            finalScript = finalScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
            finalScript = finalScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

            // 使用通用函数替换服务器地址
            finalScript = replaceServerAddresses(finalScript, req);

            console.log(`设备 ${deviceId} UID私信参数注入完成，脚本长度:`, finalScript.length);

          } else if (functionType === 'searchGroupChat') {
            // 搜索加群功能：参数注入并自动执行
            console.log(`=== 设备 ${deviceId} 搜索加群功能：参数注入 ===`);

            const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceConfig, null, 2)};

console.log('收到服务器参数:', JSON.stringify(serverParams, null, 2));

// 提取参数
const searchKeyword = serverParams.searchKeyword || "私域";
const targetJoinCount = serverParams.targetJoinCount || 5;
const operationInterval = serverParams.operationInterval || 10;
const sendMessage = serverParams.sendMessage || false;
const joinMessage = serverParams.joinMessage || "";

console.log('搜索关键词:', searchKeyword);
console.log('目标加群数量:', targetJoinCount);
console.log('操作间隔:', operationInterval);

// 自动执行脚本
setTimeout(() => {
    console.log('开始自动执行搜索加群脚本');
    // 脚本会自动使用这些参数
}, 1000);

`;
            finalScript = paramInjection + scriptContent;

            // 替换脚本中的占位符和硬编码地址
            finalScript = finalScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
            finalScript = finalScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

            // 使用通用函数替换服务器地址
            finalScript = replaceServerAddresses(finalScript, req);

          } else if (functionType === 'groupMessage') {
            // 循环群发功能：直接使用无UI脚本
            console.log(`=== 设备 ${deviceId} 循环群发功能：直接使用无UI脚本 ===`);

            // 替换脚本中的CONFIG对象
            let modifiedScript = scriptContent.replace(
              /const CONFIG = \{[\s\S]*?\};/,
              `const CONFIG = {
    sendInterval: ${deviceConfig.sendInterval || 10},
    loopMode: ${deviceConfig.executionMode === 'loop'},
    autoSave: true
};`
            );

            // 在脚本末尾添加执行调用
            const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceConfig, null, 2)};

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 直接执行脚本
console.log("=== 小红书循环群发脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("发送间隔: " + CONFIG.sendInterval + "秒");
console.log("循环模式: " + (CONFIG.loopMode ? "开启" : "关闭"));

// 设置运行状态
isRunning = true;

// 执行主函数
try {
    executeScript(CONFIG.sendInterval);
    console.log("循环群发脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "循环群发脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });

} catch (error) {
    console.log("循环群发脚本执行失败: " + error.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "循环群发脚本执行失败: " + error.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (e) {
            console.log("执行失败状态上报失败: " + e.message);
        }
    });

    throw error;
}
`;

            finalScript = modifiedScript + executeCall;

            // 替换脚本中的占位符
            finalScript = finalScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
            finalScript = finalScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

            // 使用通用函数替换服务器地址
            finalScript = replaceServerAddresses(finalScript, req);

            console.log(`设备 ${deviceId} 循环群发参数注入完成，脚本长度:`, finalScript.length);

          } else if (functionType === 'articleComment') {
            // 文章评论功能：直接使用无UI脚本
            console.log(`=== 设备 ${deviceId} 文章评论功能：直接使用无UI脚本 ===`);

            // 替换脚本中的CONFIG对象
            let modifiedScript = scriptContent.replace(
              /const CONFIG = \{[\s\S]*?\};/,
              `const CONFIG = {
    keyword: "${deviceConfig.searchKeyword || '美食推荐'}",
    commentCount: ${deviceConfig.commentCount || 3},
    delay: ${deviceConfig.operationDelay || 5}
};`
            );

            // 在脚本末尾添加执行调用
            const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceConfig, null, 2)};

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 直接执行脚本
console.log("=== 小红书文章评论脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("搜索关键词: " + CONFIG.keyword);
console.log("评论文章数量: " + CONFIG.commentCount);
console.log("操作延迟: " + CONFIG.delay + "秒");

// 设置运行状态
isRunning = true;

// 执行主函数
try {
    executeSearchComment(CONFIG.keyword, CONFIG.commentCount, CONFIG.delay);
    console.log("文章评论脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "文章评论脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });

} catch (error) {
    console.log("文章评论脚本执行失败: " + error.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "文章评论脚本执行失败: " + error.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (e) {
            console.log("执行失败状态上报失败: " + e.message);
        }
    });

    throw error;
}
`;

            finalScript = modifiedScript + executeCall;

            // 替换脚本中的占位符
            finalScript = finalScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
            finalScript = finalScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

            // 使用通用函数替换服务器地址
            finalScript = replaceServerAddresses(finalScript, req);

            console.log(`设备 ${deviceId} 文章评论参数注入完成，脚本长度:`, finalScript.length);

          } else if (functionType === 'videoPublish') {
            // 视频发布功能：直接使用无UI脚本
            console.log('🎬🎬🎬 [HTTP视频发布] ===== HTTP设备视频发布功能开始 ===== 🎬🎬🎬');
            console.log(`📱 [HTTP视频发布] 设备ID: ${deviceId}`);
            console.log(`🆔 [HTTP视频发布] 任务ID: ${generatedTaskId}`);
            console.log(`🔧 [HTTP视频发布] 功能类型: ${functionType}`);
            console.log(`🔧 [HTTP视频发布] 设备特定参数:`, JSON.stringify(deviceConfig, null, 2));
            console.log(`📹 [HTTP视频发布] 选中的视频:`, deviceConfig.selectedVideos);
            console.log(`📹 [HTTP视频发布] 视频数量:`, deviceConfig.selectedVideos ? deviceConfig.selectedVideos.length : 0);
            console.log(`📹 [HTTP视频发布] selectedVideoIds:`, deviceConfig.selectedVideoIds);
            console.log(`🔍 [HTTP视频发布] 检查视频选择状态:`);
            console.log(`   - selectedVideos存在: ${!!deviceConfig.selectedVideos}`);
            console.log(`   - selectedVideos类型: ${typeof deviceConfig.selectedVideos}`);
            console.log(`   - selectedVideos长度: ${deviceConfig.selectedVideos ? deviceConfig.selectedVideos.length : 'N/A'}`);
            console.log(`   - selectedVideoIds存在: ${!!deviceConfig.selectedVideoIds}`);
            console.log(`   - selectedVideoIds类型: ${typeof deviceConfig.selectedVideoIds}`);
            console.log(`   - selectedVideoIds长度: ${deviceConfig.selectedVideoIds ? deviceConfig.selectedVideoIds.length : 'N/A'}`);

            // 为视频发布脚本注入参数，包含选择的视频信息
            console.log(`🔧 [DEBUG] 视频发布脚本：注入参数包含视频信息`);
            console.log(`🔧 [DEBUG] 原始脚本长度:`, scriptContent.length);
            console.log(`🔧 [DEBUG] 选择的视频数量:`, deviceConfig.selectedVideos ? deviceConfig.selectedVideos.length : 0);

            // 从前端参数中提取发布配置
            const videoTitle = deviceConfig.titleTemplate || '精彩视频分享';
            const videoDescription = deviceConfig.videoDescription || '分享一个有趣的视频';

            // 处理hashtags字符串，转换为数组
            let videoTags = ['生活', '分享', '有趣']; // 默认标签
            if (deviceConfig.hashtags) {
              if (typeof deviceConfig.hashtags === 'string') {
                videoTags = deviceConfig.hashtags.split(/[,，\s]+/).filter(tag => tag.trim());
              } else if (Array.isArray(deviceConfig.hashtags)) {
                videoTags = deviceConfig.hashtags;
              }
            }

            const paramInjection = `
// 服务器传递的参数
var serverParams = {
    deviceId: "${deviceId.replace(/"/g, '\\"')}",
    taskId: "${generatedTaskId.replace(/"/g, '\\"')}",
    serverHost: "************:3002",
    selectedVideos: ${JSON.stringify(deviceConfig.selectedVideos || [])},
    videoDescription: "${videoDescription.replace(/"/g, '\\"')}",
    videoTitle: "${videoTitle.replace(/"/g, '\\"')}",
    videoTags: ${JSON.stringify(videoTags)}
};

console.log('收到服务器参数:', JSON.stringify(serverParams, null, 2));

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 将参数赋值给CONFIG对象（兼容原始脚本）
if (typeof CONFIG !== 'undefined') {
    Object.assign(CONFIG, serverParams);
} else {
    var CONFIG = serverParams;
}

`;

            finalScript = paramInjection + scriptContent;

            // 替换脚本中的占位符
            finalScript = finalScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
            finalScript = finalScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

            // 不使用动态地址替换，保持与原始服务器一致的硬编码地址
            // finalScript = replaceServerAddresses(finalScript, req);

            console.log(`HTTP设备 ${deviceId} 视频发布参数注入完成，脚本长度:`, finalScript.length);
            console.log(`🔧 [DEBUG] 最终脚本前200字符:`, finalScript.substring(0, 200));

            // 记录视频传输到数据库（在脚本执行前记录，状态为pending）
            console.log('🚀🚀🚀 [脚本执行] 准备记录视频传输到数据库 🚀🚀🚀');
            console.log('📹 [脚本执行] 选中的视频:', deviceConfig.selectedVideos);
            console.log('📱 [脚本执行] 设备ID:', deviceId);
            console.log('🆔 [脚本执行] 任务ID:', generatedTaskId);
            await recordVideoTransfersForScript(deviceConfig.selectedVideos, deviceId, generatedTaskId, 'pending', pool);
            console.log('✅ [脚本执行] 视频传输记录完成（状态：pending）');

          } else {
            // 其他功能：通用参数注入
            console.log(`=== 设备 ${deviceId} 通用功能：参数注入 ===`);

            const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceConfig, null, 2)};

console.log('收到服务器参数:', JSON.stringify(serverParams, null, 2));

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 自动执行脚本
setTimeout(() => {
    console.log('开始自动执行脚本');
    // 脚本会自动使用serverParams中的参数
}, 1000);

`;
            finalScript = paramInjection + scriptContent;

            // 替换脚本中的占位符和硬编码地址
            finalScript = finalScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
            finalScript = finalScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

            // 使用通用函数替换服务器地址
            finalScript = replaceServerAddresses(finalScript, req);
          }

          console.log(`✅ 脚本转换完成，设备 ${deviceId}，功能 ${functionType}，最终脚本长度:`, finalScript.length);

          // 发送脚本到设备
          if (device.socketId.startsWith('http_')) {
            // HTTP设备：将命令存储到待执行队列
            if (!pendingCommands.has(deviceId)) {
              pendingCommands.set(deviceId, []);
            }
            pendingCommands.get(deviceId).push({
              logId: generatedTaskId,
              script: finalScript,
              timestamp: Date.now(),
              functionType: functionType,
              deviceConfig: deviceConfig
            });
            console.log(`📤 HTTP设备命令已排队: ${device.deviceName} (${deviceId})`);

            // HTTP设备也需要更新状态
            device.status = 'busy';
            device.lastActivity = new Date();
            console.log(`📱 HTTP设备状态已更新: ${deviceId} -> busy`);

            // 立即更新设备状态为忙碌（所有功能类型）
            console.log(`🔄 [设备状态] HTTP设备 ${deviceId} 状态已更新为忙碌（${functionType}任务）`);

            // 同时更新数据库状态并广播给前端
            await updateDeviceStatus(deviceId, 'busy');
            // updateDeviceStatus函数已经会广播设备状态更新，不需要重复发送
          } else {
            // WebSocket设备：需要重新进行脚本转换（与HTTP设备逻辑相同）
            console.log(`=== WebSocket设备 ${deviceId} 脚本转换开始 ===`);

            // 重新进行脚本转换（WebSocket设备专用逻辑）
            let webSocketScript;

            if (functionType === 'profile') {
              // 修改资料功能：直接使用无UI脚本
              console.log(`=== WebSocket设备 ${deviceId} 修改资料功能：直接使用无UI脚本 ===`);

              // 移除原始的main()调用
              let modifiedScript = scriptContent.replace(/main\(\);?\s*$/m, '');

              // 处理控制参数转换
              const modifyOptions = deviceConfig.modifyOptions || [];
              const onlyNickname = modifyOptions.includes('onlyNickname');
              const onlyProfile = modifyOptions.includes('onlyProfile');

              // 处理安全选项转换
              const safetyOptions = deviceConfig.safetyOptions || [];
              const backupOriginal = safetyOptions.includes('backupOriginal');
              const confirmBeforeChange = safetyOptions.includes('confirmBeforeChange');
              const validateInput = safetyOptions.includes('validateInput');

              // 创建包含转换后参数的配置对象
              const processedConfig = {
                ...deviceConfig,
                backupOriginal: backupOriginal,
                confirmBeforeChange: confirmBeforeChange,
                validateInput: validateInput
              };

              // 在脚本开头注入参数并直接调用executeScript
              const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(processedConfig, null, 2)};

// 控制参数转换
const onlyNickname = ${onlyNickname};
const onlyProfile = ${onlyProfile};

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 直接执行脚本，跳过UI输入
console.log("=== 小红书资料修改脚本启动（无UI模式）===");
console.log("收到服务器参数:", JSON.stringify(serverParams, null, 2));

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("昵称: " + serverParams.nickname);
console.log("简介: " + serverParams.profile);
console.log("只修改昵称: " + onlyNickname);
console.log("只修改简介: " + onlyProfile);

// 参数验证
if (!serverParams.nickname && !serverParams.profile) {
    console.error("错误: 昵称和简介都为空");
    throw new Error("昵称和简介都为空");
}

// 设置运行状态
isRunning = true;

try {
    executeScript(serverParams.nickname, serverParams.profile, onlyNickname, onlyProfile, serverParams);
    console.log("脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "修改资料脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });
} catch (e) {
    console.error("脚本执行出错: " + e.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "修改资料脚本执行失败: " + e.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (err) {
            console.log("执行失败状态上报失败: " + err.message);
        }
    });
    throw e;
} finally {
    isRunning = false;
    console.log("=== 脚本执行结束 ===");
}

`;
              webSocketScript = modifiedScript + '\n' + paramInjection;

              // 替换脚本中的占位符
              webSocketScript = webSocketScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              webSocketScript = webSocketScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

              console.log(`WebSocket设备 ${deviceId} 修改资料参数注入完成，脚本长度:`, webSocketScript.length);
            } else if (functionType === 'groupMessage') {
              // 循环群发功能：直接使用无UI脚本
              console.log(`=== WebSocket设备 ${deviceId} 循环群发功能：直接使用无UI脚本 ===`);

              // 替换脚本中的CONFIG对象
              let modifiedScript = scriptContent.replace(
                /const CONFIG = \{[\s\S]*?\};/,
                `const CONFIG = {
    sendInterval: ${deviceConfig.sendInterval || 10},
    loopMode: ${deviceConfig.executionMode === 'loop'},
    autoSave: true
};`
              );

              // 在脚本末尾添加执行调用
              const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceConfig, null, 2)};

// 直接执行脚本
console.log("=== 小红书循环群发脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("发送间隔: " + CONFIG.sendInterval + "秒");
console.log("循环模式: " + (CONFIG.loopMode ? "开启" : "关闭"));

// 设置运行状态
isRunning = true;

// 执行主函数
try {
    executeScript(CONFIG.sendInterval);
    console.log("循环群发脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "循环群发脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });

} catch (error) {
    console.log("循环群发脚本执行失败: " + error.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "循环群发脚本执行失败: " + error.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (e) {
            console.log("执行失败状态上报失败: " + e.message);
        }
    });

    throw error;
}
`;

              webSocketScript = modifiedScript + executeCall;

              // 替换脚本中的占位符
              webSocketScript = webSocketScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              webSocketScript = webSocketScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

              console.log(`WebSocket设备 ${deviceId} 循环群发参数注入完成，脚本长度:`, webSocketScript.length);
            } else if (functionType === 'articleComment') {
              // 文章评论功能：直接使用无UI脚本
              console.log(`=== WebSocket设备 ${deviceId} 文章评论功能：直接使用无UI脚本 ===`);

              // 替换脚本中的CONFIG对象
              let modifiedScript = scriptContent.replace(
                /const CONFIG = \{[\s\S]*?\};/,
                `const CONFIG = {
    keyword: "${deviceConfig.searchKeyword || '美食推荐'}",
    commentCount: ${deviceConfig.commentCount || 3},
    delay: ${deviceConfig.operationDelay || 5}
};`
              );

              // 在脚本末尾添加执行调用
              const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceConfig, null, 2)};

// 直接执行脚本
console.log("=== 小红书文章评论脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("搜索关键词: " + CONFIG.keyword);
console.log("评论文章数量: " + CONFIG.commentCount);
console.log("操作延迟: " + CONFIG.delay + "秒");

// 设置运行状态
isRunning = true;

// 执行主函数
try {
    executeSearchComment(CONFIG.keyword, CONFIG.commentCount, CONFIG.delay);
    console.log("文章评论脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "文章评论脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });

} catch (error) {
    console.log("文章评论脚本执行失败: " + error.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "文章评论脚本执行失败: " + error.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (e) {
            console.log("执行失败状态上报失败: " + e.message);
        }
    });

    throw error;
}
`;

              webSocketScript = modifiedScript + executeCall;

              // 替换脚本中的占位符
              webSocketScript = webSocketScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              webSocketScript = webSocketScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

              console.log(`WebSocket设备 ${deviceId} 文章评论参数注入完成，脚本长度:`, webSocketScript.length);
            } else if (functionType === 'uidMessage' || functionType === 'uidFileMessage') {
              // UID私信功能：直接使用无UI脚本
              console.log(`=== WebSocket设备 ${deviceId} ${functionType === 'uidFileMessage' ? '文件上传' : '手动输入'}UID私信功能：直接使用无UI脚本 ===`);

              // 处理UID获取逻辑
              if (deviceConfig.inputMode === 'file' || functionType === 'uidFileMessage') {
                console.log('文件模式：从数据库获取UID列表');

                try {
                  // 使用数据库获取UID
                  if (pool && deviceConfig.selectedFileId) {
                    const connection = await pool.getConnection();

                    try {
                      // 获取可用的UID（包含ID用于后续标记）
                      const maxCount = deviceConfig.totalUidCount || 10;
                      const [uidRows] = await connection.execute(
                        'SELECT id, uid FROM uid_data WHERE file_id = ? AND is_used = 0 ORDER BY id ASC LIMIT ?',
                        [deviceConfig.selectedFileId, maxCount]
                      );

                      if (uidRows.length > 0) {
                        // 提取UID列表
                        deviceConfig.uidList = uidRows.map(row => row.uid);
                        deviceConfig.uidIds = uidRows.map(row => row.id); // 保存ID用于后续标记

                        console.log(`[WebSocket] 从数据库获取到 ${deviceConfig.uidList.length} 个可用UID`);
                        console.log('[WebSocket] UID列表:', deviceConfig.uidList);

                        // 预先标记这些UID为当前任务使用（但不标记为已使用）
                        const uidIdList = deviceConfig.uidIds.join(',');
                        await connection.execute(
                          `UPDATE uid_data SET task_id = ? WHERE id IN (${uidIdList})`,
                          [generatedTaskId]
                        );
                        console.log(`[WebSocket] 已为任务 ${generatedTaskId} 预分配 ${deviceConfig.uidIds.length} 个UID`);

                      } else {
                        console.warn(`[WebSocket] 文件 ${deviceConfig.selectedFileId} 中没有可用的UID`);
                        deviceConfig.uidList = [];
                      }
                    } finally {
                      connection.release();
                    }
                  } else {
                    console.warn('[WebSocket] 缺少数据库连接或文件ID，无法获取UID列表');
                    deviceConfig.uidList = [];
                  }
                } catch (dbError) {
                  console.error('[WebSocket] 从数据库获取UID失败:', dbError);
                  deviceConfig.uidList = [];
                }
              } else {
                console.log('[WebSocket] 手动模式：使用前端传递的UID列表');
                console.log('[WebSocket] UID列表:', deviceConfig.uidList);
              }

              // 获取服务器IP地址
              const os = require('os');
              const networkInterfaces = os.networkInterfaces();
              let serverIP = 'localhost';

              for (const interfaceName in networkInterfaces) {
                const iface = networkInterfaces[interfaceName];
                for (const alias of iface) {
                  if (alias.family === 'IPv4' && !alias.internal) {
                    serverIP = alias.address;
                    break;
                  }
                }
                if (serverIP !== 'localhost') break;
              }

              deviceConfig.serverHost = serverIP + ':3002';
              console.log(`[WebSocket] 为设备 ${deviceId} 设置服务器地址: ${deviceConfig.serverHost}`);

              // UID私信功能是无UI版本，直接注入参数
              const paramInjection = `
// 服务器传递的UID私信参数
const globalConfig = ${JSON.stringify(deviceConfig, null, 2)};

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

`;
              webSocketScript = paramInjection + scriptContent;

              // 替换脚本中的占位符
              webSocketScript = webSocketScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              webSocketScript = webSocketScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

              console.log(`WebSocket设备 ${deviceId} UID私信参数注入完成，脚本长度:`, webSocketScript.length);
            } else if (functionType === 'videoPublish') {
              // 视频发布功能：直接使用无UI脚本
              console.log('🎬🎬🎬 [WebSocket视频发布] ===== WebSocket设备视频发布功能开始 ===== 🎬🎬🎬');
              console.log(`📱 [WebSocket视频发布] 设备ID: ${deviceId}`);
              console.log(`🆔 [WebSocket视频发布] 任务ID: ${generatedTaskId}`);
              console.log(`🔧 [WebSocket视频发布] 功能类型: ${functionType}`);
              console.log(`🔧 [WebSocket视频发布] 设备特定参数:`, JSON.stringify(deviceConfig, null, 2));
              console.log(`📹 [WebSocket视频发布] 选中的视频:`, deviceConfig.selectedVideos);
              console.log(`📹 [WebSocket视频发布] 视频数量:`, deviceConfig.selectedVideos ? deviceConfig.selectedVideos.length : 0);

              // 为WebSocket视频发布脚本注入参数，包含选择的视频信息
              console.log(`🔧 [DEBUG] WebSocket视频发布脚本：注入参数包含视频信息`);
              console.log(`🔧 [DEBUG] WebSocket原始脚本长度:`, scriptContent.length);
              console.log(`🔧 [DEBUG] WebSocket选择的视频数量:`, deviceConfig.selectedVideos ? deviceConfig.selectedVideos.length : 0);

              // 从前端参数中提取发布配置
              const videoTitle = deviceConfig.titleTemplate || '精彩视频分享';
              const videoDescription = deviceConfig.videoDescription || '分享一个有趣的视频';

              // 处理hashtags字符串，转换为数组
              let videoTags = ['生活', '分享', '有趣']; // 默认标签
              if (deviceConfig.hashtags) {
                if (typeof deviceConfig.hashtags === 'string') {
                  videoTags = deviceConfig.hashtags.split(/[,，\s]+/).filter(tag => tag.trim());
                } else if (Array.isArray(deviceConfig.hashtags)) {
                  videoTags = deviceConfig.hashtags;
                }
              }

              const paramInjection = `
// 服务器传递的参数
var serverParams = {
    deviceId: "${deviceId.replace(/"/g, '\\"')}",
    taskId: "${generatedTaskId.replace(/"/g, '\\"')}",
    serverHost: "************:3002",
    selectedVideos: ${JSON.stringify(deviceConfig.selectedVideos || [])},
    videoDescription: "${videoDescription.replace(/"/g, '\\"')}",
    videoTitle: "${videoTitle.replace(/"/g, '\\"')}",
    videoTags: ${JSON.stringify(videoTags)}
};

console.log('收到服务器参数:', JSON.stringify(serverParams, null, 2));

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 将参数赋值给CONFIG对象（兼容原始脚本）
if (typeof CONFIG !== 'undefined') {
    Object.assign(CONFIG, serverParams);
} else {
    var CONFIG = serverParams;
}

`;

              webSocketScript = paramInjection + scriptContent;

              // 替换脚本中的占位符
              webSocketScript = webSocketScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              webSocketScript = webSocketScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);

              console.log(`WebSocket设备 ${deviceId} 视频发布参数注入完成，脚本长度:`, webSocketScript.length);
              console.log(`🔧 [DEBUG] WebSocket最终脚本前200字符:`, webSocketScript.substring(0, 200));

              // 记录视频传输到数据库（在脚本执行前记录，状态为pending）
              console.log('🚀🚀🚀 [WebSocket脚本执行] 准备记录视频传输到数据库 🚀🚀🚀');
              console.log('📹 [WebSocket脚本执行] 选中的视频:', deviceConfig.selectedVideos);
              console.log('📱 [WebSocket脚本执行] 设备ID:', deviceId);
              console.log('🆔 [WebSocket脚本执行] 任务ID:', generatedTaskId);
              await recordVideoTransfersForScript(deviceConfig.selectedVideos, deviceId, generatedTaskId, 'pending', pool);
              console.log('✅ [WebSocket脚本执行] 视频传输记录完成（状态：pending）');
            } else {
              // 其他功能：使用通用参数注入
              console.log(`=== WebSocket设备 ${deviceId} 通用功能：参数注入 ===`);

              const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceConfig, null, 2)};

console.log('收到服务器参数:', JSON.stringify(serverParams, null, 2));

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);

// 自动执行脚本
setTimeout(() => {
    console.log('开始自动执行脚本');
    // 脚本会自动使用serverParams中的参数
}, 1000);

`;
              webSocketScript = paramInjection + scriptContent;

              // 替换脚本中的占位符
              webSocketScript = webSocketScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              webSocketScript = webSocketScript.replace(/TASK_ID_PLACEHOLDER/g, generatedTaskId);
            }

            // 不使用动态地址替换，保持与原始服务器一致的硬编码地址
            // webSocketScript = replaceServerAddresses(webSocketScript, req);

            console.log(`✅ WebSocket设备脚本转换完成，设备 ${deviceId}，功能 ${functionType}，最终脚本长度:`, webSocketScript.length);

            // WebSocket设备：直接发送
            const deviceSocket = io.sockets.sockets.get(device.socketId);
            if (deviceSocket) {
              deviceSocket.emit('execute_script', {
                logId: generatedTaskId,
                script: webSocketScript,
                params: deviceConfig,
                timestamp: Date.now(),
                functionType: functionType
              });
              console.log(`📤 WebSocket设备命令已发送: ${device.deviceName} (${deviceId})`);

              // 更新设备状态为忙碌
              device.status = 'busy';
              device.lastActivity = new Date();
              console.log(`📱 设备状态已更新: ${deviceId} -> busy`);

              // 立即更新设备状态为忙碌（所有功能类型）
              console.log(`🔄 [设备状态] WebSocket设备 ${deviceId} 状态已更新为忙碌（${functionType}任务）`);

              // 同时更新数据库状态并广播给前端
              await updateDeviceStatus(deviceId, 'busy');
              // updateDeviceStatus函数已经会广播设备状态更新，不需要重复发送
            } else {
              throw new Error('设备连接已断开');
            }
          }

          // 广播初始状态到前端
          io.emit('xiaohongshu_status_update', {
            deviceId: deviceId,
            taskId: generatedTaskId,
            status: 'starting',
            progress: 0,
            message: '脚本已发送，等待设备执行',
            stage: 'starting',
            timestamp: new Date().toISOString()
          });

          console.log(`📡 已广播初始状态: ${deviceId} -> starting`);

          successCount++;
          results.push({
            deviceId,
            status: 'sent',
            message: '脚本已发送到设备'
          });

        } catch (error) {
          console.error(`❌ 发送脚本到设备 ${deviceId} 失败:`, error);
          failCount++;
          results.push({
            deviceId,
            status: 'failed',
            message: error.message
          });
        }
      }

      task.status = 'running';
      task.startedAt = new Date();
      task.results = results;

      console.log(`✅ 小红书自动化任务创建完成: ${task.id}`);
      console.log(`- 成功: ${successCount}个设备`);
      console.log(`- 失败: ${failCount}个设备`);

      res.json({
        success: true,
        message: `任务已创建，成功: ${successCount}个，失败: ${failCount}个`,
        data: {
          taskId: task.id,
          functionType,
          successCount,
          failCount,
          totalCount: deviceIds.length,
          results
        }
      });

    } catch (error) {
      console.error('❌ 执行小红书自动化任务失败:', error);
      res.status(500).json({
        success: false,
        message: '执行任务失败: ' + error.message
      });
    }
  });

  // 小红书实时状态API（统一处理所有功能）(原始文件第7699行) - 改为设备认证模式
  app.post('/api/xiaohongshu/realtime-status', authenticateDevice(pool), async (req, res) => {
    try {
      const {
        deviceId,
        taskId,
        // 视频发布功能字段
        publishedVideoCount,
        totalVideoCount,
        currentStep,
        errorMessage,
        // 循环群发功能字段
        sentMessageCount,
        processedControlCount,
        executionCount,
        loopCount,
        // 修改资料功能字段
        operationCount,
        processedStepCount,
        // 搜索群聊功能字段
        joinedGroupCount,
        scrollAttemptCount,
        // 文章评论功能字段
        commentedArticleCount,
        searchAttemptCount,
        // UID私信功能字段
        processedUidCount,
        successCount,
        failedCount,
        // 通用字段
        currentStatus,
        message,
        timestamp
      } = req.body;
      const { userId, username } = req.device; // 来自设备认证中间件

      // 设备认证中间件已经验证了设备权限，无需重复验证

      console.log(`📊 [小红书实时状态] 用户${userId}设备${deviceId}状态更新:`, {
        deviceId,
        taskId,
        currentStep,
        currentStatus,
        message,
        publishedVideoCount,
        totalVideoCount
      });

      // 构造完整的状态数据
      const statusData = {
        deviceId,
        taskId,
        // 视频发布功能字段
        publishedVideoCount,
        totalVideoCount,
        currentStep,
        errorMessage,
        // 循环群发功能字段
        sentMessageCount,
        processedControlCount,
        executionCount,
        loopCount,
        // 修改资料功能字段
        operationCount,
        processedStepCount,
        // 搜索群聊功能字段
        joinedGroupCount,
        scrollAttemptCount,
        // 文章评论功能字段
        commentedArticleCount,
        searchAttemptCount,
        // UID私信功能字段
        processedUidCount,
        successCount,
        failedCount,
        // 通用字段
        currentStatus,
        message,
        timestamp: timestamp || new Date().toISOString()
      };

      // 只向设备所属用户的客户端广播实时状态
      console.log(`📡 [实时状态广播] 准备广播给用户${userId}，事件: xiaohongshu_realtime_status`);
      console.log(`📡 [实时状态广播] 状态数据:`, JSON.stringify(statusData, null, 2));

      const sentCount = broadcastToUserClients(userId, 'xiaohongshu_realtime_status', statusData);

      console.log(`✅ [小红书实时状态] 已广播给用户${userId}的${sentCount}个客户端`);

      res.json({
        success: true,
        message: '实时状态更新成功'
      });

    } catch (error) {
      console.error('处理小红书实时状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 接收脚本执行状态更新API (原始文件第4573行) - 连接码模式，无需token
  app.post('/api/xiaohongshu/status', async (req, res) => {
    try {
      const { deviceId, status, progress, message, stage, taskId, debugInfo } = req.body;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      // 构造状态更新数据
      const statusData = {
        deviceId: deviceId,
        status: status,
        progress: progress,
        message: message,
        stage: stage,
        taskId: taskId,
        debugInfo: debugInfo,
        timestamp: new Date().toISOString()
      };

      // 更新小红书执行日志
      if (xiaohongshuLogService && taskId) {
        try {
          // 首先检查任务是否已经被手动停止
          const currentLog = await xiaohongshuLogService.getExecutionLogDetail(taskId);

          if (currentLog && currentLog.executionStatus === 'stopped') {
            console.log(`⚠️ 任务 ${taskId} 已被手动停止，忽略状态更新: ${stage} - ${status}`);

            // 如果手机端发送的是停止状态，允许更新
            if (stage === 'stopped' || status === 'stopped') {
              console.log(`✅ 手机端确认停止状态，允许更新: ${taskId}`);
            } else {
              // 忽略其他状态更新，保持停止状态
              console.log(`🚫 忽略状态更新，保持停止状态: ${taskId}`);

              res.json({
                success: true,
                message: '任务已停止，忽略状态更新'
              });
              return;
            }
          }

          // 根据stage确定执行状态
          let executionStatus = 'running';
          if (stage === 'completed') {
            // 判断是否成功：status为'success'或者message中包含成功信息
            const isSuccess = status === 'success' ||
                             (message && (
                               message.includes('成功') ||
                               message.includes('完成') ||
                               message.includes('执行完成')
                             ));
            executionStatus = isSuccess ? 'completed' : 'failed';
          } else if (stage === 'error') {
            executionStatus = 'failed';
          } else if (stage === 'stopped' || status === 'stopped') {
            executionStatus = 'stopped';
          }

          await xiaohongshuLogService.updateExecutionStatus(
            taskId,
            executionStatus,
            progress || 0,
            stage || '执行中',
            message
          );

          console.log(`小红书执行日志已更新: ${taskId} -> ${executionStatus} (${progress}%)`);
        } catch (logError) {
          console.error('更新小红书执行日志失败:', logError);
        }
      }

      // 通知所有Web客户端状态变化（使用用户隔离广播）
      // 首先需要获取设备所属的用户ID
      let deviceUserId = null;
      if (pool) {
        try {
          const [deviceRows] = await pool.execute(
            'SELECT user_id FROM devices WHERE device_id = ?',
            [deviceId]
          );
          if (deviceRows.length > 0) {
            deviceUserId = deviceRows[0].user_id;
          }
        } catch (dbError) {
          console.error('查询设备所属用户失败:', dbError);
        }
      }

      if (deviceUserId) {
        broadcastToUserClients(deviceUserId, 'xiaohongshu_status_update', statusData);
        console.log(`📡 [状态更新] 已向用户${deviceUserId}的客户端广播状态更新`);
      } else {
        // 如果无法确定用户，使用全局广播（兼容性）
        io.emit('xiaohongshu_status_update', statusData);
        console.log(`📡 [状态更新] 无法确定设备用户，使用全局广播`);
      }

      console.log(`📱 收到小红书状态更新: ${deviceId} - ${stage} - ${status} (${progress}%)`);
      if (message) {
        console.log(`📝 状态消息: ${message}`);
      }

      // 如果脚本执行完成（成功或失败），将设备状态更新为在线并通知前端
      if (stage === 'completed' || stage === 'error') {
        console.log(`小红书脚本执行${stage === 'completed' ? '完成' : '出错'}，恢复设备状态: ${deviceId}`);

        // 立即恢复设备状态为在线
        try {
          await updateDeviceStatus(deviceId, 'online');
          console.log(`设备状态已恢复为在线: ${deviceId}`);
        } catch (error) {
          console.error('恢复设备状态失败:', error);
        }

        // 发送脚本执行完成事件给前端，用于更新Vuex状态
        const completionEvent = {
          deviceId: deviceId,
          taskId: taskId,
          status: stage === 'completed' && (status === 'success' ||
                  (message && (message.includes('成功') || message.includes('完成') || message.includes('执行完成'))))
                  ? 'success' : 'failed',
          message: message || (stage === 'completed' ? '脚本执行完成' : '脚本执行失败'),
          timestamp: new Date().toISOString()
        };

        console.log(`📡 发送脚本执行完成事件给前端: ${JSON.stringify(completionEvent)}`);

        // 使用用户隔离广播
        if (deviceUserId) {
          broadcastToUserClients(deviceUserId, 'xiaohongshu_execution_completed', completionEvent);
          console.log(`📡 [脚本完成] 已向用户${deviceUserId}的客户端广播脚本完成事件`);
        } else {
          // 如果无法确定用户，使用全局广播（兼容性）
          io.emit('xiaohongshu_execution_completed', completionEvent);
          console.log(`📡 [脚本完成] 无法确定设备用户，使用全局广播`);
        }
      }

      res.json({
        success: true,
        message: '状态更新已接收'
      });

    } catch (error) {
      console.error('❌ 处理小红书状态更新失败:', error);
      res.status(500).json({
        success: false,
        message: '处理状态更新失败: ' + error.message
      });
    }
  });

  // 接收脚本调试日志API (原始文件第4968行) - 连接码模式，无需token
  app.post('/api/xiaohongshu/debug-log', async (req, res) => {
    try {
      const { deviceId, taskId, message, level, timestamp } = req.body;

      if (!deviceId || !message) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 构造调试日志数据
      const logData = {
        deviceId: deviceId,
        taskId: taskId,
        message: message,
        level: level || 'info',
        timestamp: timestamp || new Date().toISOString()
      };

      // 通过WebSocket发送调试日志到前端
      io.emit('xiaohongshu_debug_log', logData);

      // 只在debug模式下输出调试日志到控制台
      if (process.env.DEBUG_LOGS === 'true') {
        console.log(`调试日志 [${deviceId}]: ${message}`);
      }

      res.json({
        success: true,
        message: '调试日志已接收'
      });

    } catch (error) {
      console.error('处理调试日志失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // UID文件上传API (原始文件第5013行) - 已添加用户隔离
  app.post('/api/xiaohongshu/upload-uid-file', authenticateToken, userIsolationMiddleware, upload.single('file'), async (req, res) => {
    try {
      const userId = req.currentUserId;

      if (!req.file) {
        return res.status(400).json({ success: false, message: '没有上传文件' });
      }

      const filePath = req.file.path;
      const fileName = req.file.filename;
      // 修复文件名编码问题
      const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');

      console.log(`[UID文件上传] 用户${userId}上传文件:`, {
        fileName,
        originalName,
        filePath,
        size: req.file.size
      });

      // 检查文件格式
      const fileExt = path.extname(originalName).toLowerCase();
      if (fileExt !== '.txt' && fileExt !== '.csv') {
        fs.unlinkSync(filePath); // 删除不支持的文件
        return res.status(400).json({
          success: false,
          message: '只支持 .txt 和 .csv 格式的文件'
        });
      }

      // 读取文件内容并检查是否为二进制文件
      let fileContent;
      try {
        fileContent = fs.readFileSync(filePath, 'utf8');

        // 检查是否包含二进制数据（Excel文件等）
        if (fileContent.includes('\x00') || fileContent.includes('PK\x03\x04')) {
          fs.unlinkSync(filePath); // 删除二进制文件
          return res.status(400).json({
            success: false,
            message: '检测到二进制文件格式。请确保上传的是纯文本格式的 .txt 或 .csv 文件，而不是 Excel 文件。如果您有 Excel 文件，请将其另存为 CSV (逗号分隔值) 格式。'
          });
        }
      } catch (error) {
        fs.unlinkSync(filePath); // 删除无法读取的文件
        return res.status(400).json({
          success: false,
          message: '文件格式错误，无法读取文件内容'
        });
      }

      const lines = fileContent.split('\n').filter(line => line.trim());
      console.log(`文件包含 ${lines.length} 行数据`);

      // 解析UID数据并验证格式
      const uids = [];
      const invalidLines = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
          // 验证UID格式：应该是字母数字组合，长度合理
          if (line.length > 100) {
            invalidLines.push(`第${i + 1}行: 内容过长 (${line.length}字符)`);
            continue;
          }

          // 检查是否包含非打印字符或二进制数据
          if (/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\xFF]/.test(line)) {
            invalidLines.push(`第${i + 1}行: 包含非法字符`);
            continue;
          }

          // 检查是否为有效的UID格式（字母数字下划线组合）
          if (!/^[a-zA-Z0-9_.-]+$/.test(line)) {
            invalidLines.push(`第${i + 1}行: UID格式无效 "${line.substring(0, 20)}${line.length > 20 ? '...' : ''}"`);
            continue;
          }

          uids.push({
            uid: line,
            status: 'unused',
            lineNumber: i + 1
          });
        }
      }

      // 如果有无效行，返回详细错误信息
      if (invalidLines.length > 0) {
        fs.unlinkSync(filePath); // 删除包含无效数据的文件
        return res.status(400).json({
          success: false,
          message: `文件包含无效的UID格式：\n${invalidLines.slice(0, 5).join('\n')}${invalidLines.length > 5 ? `\n... 还有 ${invalidLines.length - 5} 个错误` : ''}\n\n请确保文件是纯文本格式，每行一个有效的UID。`
        });
      }

      if (uids.length === 0) {
        fs.unlinkSync(filePath); // 删除空文件
        return res.status(400).json({
          success: false,
          message: '文件中没有有效的UID数据'
        });
      }

      // 保存到数据库
      if (pool) {
        try {
          // 检查是否已存在相同文件名的文件（按用户过滤）
          const [existingFiles] = await dbEnhancer.executeWithUserFilter(
            'SELECT id, original_name FROM uid_files WHERE original_name = ? AND status = "active"',
            [originalName],
            userId
          );

          if (existingFiles.length > 0) {
            fs.unlinkSync(filePath); // 删除重复文件
            return res.status(400).json({
              success: false,
              message: `文件 "${originalName}" 已存在，请勿重复上传。如需更新，请先删除原文件。`
            });
          }

          // 插入文件记录（自动添加user_id）
          const [fileResult] = await dbEnhancer.insertWithUserId('uid_files', {
            file_name: req.file.filename,
            original_name: originalName,
            file_path: filePath,
            file_size: req.file.size,
            total_uid_count: uids.length,
            uploaded_by: req.user.username || 'unknown'
          }, userId);

          const fileId = fileResult.insertId;

          // 批量插入UID数据（自动添加user_id）
          const uidDataArray = uids.map(uid => ({
            file_id: fileId,
            uid: uid.uid
          }));
          await dbEnhancer.batchInsertWithUserId('uid_data', uidDataArray, userId);

          console.log(`UID文件已保存到数据库，ID: ${fileId}, 包含 ${uids.length} 个UID`);

          res.json({
            success: true,
            message: 'UID文件上传成功',
            data: {
              id: fileId,
              original_name: originalName,
              total_uid_count: uids.length,
              upload_time: new Date().toISOString()
            }
          });
        } catch (dbError) {
          fs.unlinkSync(filePath); // 删除文件
          throw dbError;
        }
      } else {
        // 数据库不可用时，只保存文件
        const fileId = Date.now().toString();
        uidStorage.set(fileId, {
          id: fileId,
          fileName: originalName,
          uploadedAt: new Date(),
          totalCount: uids.length,
          usedCount: 0,
          uids: uids
        });

        console.log(`UID文件已保存到内存，ID: ${fileId}, 包含 ${uids.length} 个UID`);

        res.json({
          success: true,
          message: 'UID文件上传成功（数据库不可用，仅保存文件）',
          data: {
            id: fileId,
            original_name: originalName,
            total_uid_count: uids.length,
            upload_time: new Date().toISOString()
          }
        });
      }

    } catch (error) {
      console.error('UID文件上传失败:', error);
      res.status(500).json({
        success: false,
        message: '上传失败: ' + error.message
      });
    }
  });

  // 执行日志停止按钮API - 已添加用户隔离
  app.post('/api/xiaohongshu/logs/:logId/stop', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { logId } = req.params;
      const userId = req.currentUserId;

      if (!logId) {
        return res.status(400).json({
          success: false,
          message: '缺少日志ID参数'
        });
      }

      console.log(`[用户${userId}] 收到执行日志停止请求: logId=${logId}`);

      // 获取执行日志详情
      if (!xiaohongshuLogService) {
        return res.status(500).json({
          success: false,
          message: '日志服务不可用'
        });
      }

      const logDetail = await xiaohongshuLogService.getExecutionLogDetail(logId);
      if (!logDetail) {
        return res.status(404).json({
          success: false,
          message: '执行日志不存在'
        });
      }

      const deviceId = logDetail.device_id;
      const taskId = logDetail.task_id;

      console.log(`执行日志停止: deviceId=${deviceId}, taskId=${taskId}`);

      // 调用停止脚本API的逻辑
      const stopResult = await stopScriptExecution(deviceId, taskId, logId);

      if (stopResult.success) {
        res.json({
          success: true,
          message: '停止命令已发送'
        });
      } else {
        res.status(500).json({
          success: false,
          message: stopResult.message
        });
      }

    } catch (error) {
      console.error('执行日志停止失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 停止脚本执行的内部函数
  async function stopScriptExecution(deviceId, taskId, logId) {
    try {
      // 查找设备
      let targetDevice = null;
      let targetSocket = null;

      for (const [socketId, deviceData] of devices) {
        if (deviceData.deviceId === deviceId) {
          targetDevice = deviceData;
          targetSocket = io.sockets.sockets.get(socketId);
          break;
        }
      }

      if (targetDevice && targetSocket) {
        // WebSocket设备 - 直接发送停止命令
        console.log(`向WebSocket设备发送停止命令: ${deviceId}`);
        targetSocket.emit('stop_script', {
          taskId: taskId,
          logId: logId,
          reason: '用户手动停止'
        });

        // 更新执行状态为停止中
        if (xiaohongshuLogService && logId) {
          await xiaohongshuLogService.updateExecutionStatus(
            logId,
            'stopping',
            50,
            '正在停止...',
            '用户请求停止脚本执行'
          );
        }

        return { success: true, message: '停止命令已发送到WebSocket设备' };
      } else {
        // HTTP设备 - 添加停止命令到队列
        console.log(`HTTP设备，添加停止命令到队列: ${deviceId}`);

        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }

        pendingCommands.get(deviceId).push({
          type: 'stop_script',
          taskId: taskId,
          logId: logId,
          reason: '用户手动停止',
          timestamp: Date.now()
        });

        // 更新执行状态为停止中
        if (xiaohongshuLogService && logId) {
          await xiaohongshuLogService.updateExecutionStatus(
            logId,
            'stopping',
            50,
            '正在停止...',
            '用户请求停止脚本执行（HTTP设备）'
          );
        }

        return { success: true, message: '停止命令已添加到队列' };
      }

    } catch (error) {
      console.error('停止脚本执行失败:', error);
      return { success: false, message: '停止失败: ' + error.message };
    }
  }

  // 获取UID文件列表API (原始文件第5160行) - 已添加用户隔离
  app.get('/api/xiaohongshu/uid-files', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;

      console.log(`[UID文件列表] 用户${userId}查询UID文件列表`);

      if (pool) {
        // 手动添加用户过滤条件，确保用户只能看到自己上传的UID文件
        const [files] = await pool.execute(`
          SELECT
            f.id,
            f.file_name,
            f.original_name,
            f.file_size,
            f.total_uid_count,
            f.uploaded_by,
            f.upload_time,
            f.status,
            COUNT(CASE WHEN d.is_used = 1 THEN 1 END) as used_count,
            COUNT(CASE WHEN d.is_used = 0 THEN 1 END) as unused_count
          FROM uid_files f
          LEFT JOIN uid_data d ON f.id = d.file_id AND d.user_id = ?
          WHERE f.status = 'active' AND f.user_id = ?
          GROUP BY f.id
          ORDER BY f.upload_time DESC
        `, [userId, userId]);

        console.log(`[UID文件列表] 用户${userId}查询到${files.length}个文件`);

        res.json({
          success: true,
          data: files
        });
      } else {
        // 数据库不可用时返回内存中的数据
        const files = Array.from(uidStorage.values()).map(file => ({
          id: file.id,
          file_name: file.fileName,
          original_name: file.fileName,
          total_uid_count: file.totalCount,
          used_count: file.usedCount,
          unused_count: file.totalCount - file.usedCount,
          upload_time: file.uploadedAt,
          status: 'active'
        }));

        res.json({
          success: true,
          data: files
        });
      }
    } catch (error) {
      console.error('获取UID文件列表失败:', error);
      res.status(500).json({ success: false, message: '获取文件列表失败' });
    }
  });

  // 获取指定文件的UID列表API (原始文件第5200行) - 已添加用户隔离
  app.get('/api/xiaohongshu/uid-files/:fileId/uids', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { fileId } = req.params;
      const { page = 1, limit = 50, status } = req.query;
      const userId = req.currentUserId;

      console.log(`🔍 [用户${userId}] 获取UID列表请求: fileId=${fileId}, page=${page}, limit=${limit}, status=${status}`);

      if (pool) {
        // 首先验证文件所属权
        const [fileCheck] = await pool.execute(`
          SELECT user_id FROM uid_files WHERE id = ?
        `, [fileId]);

        if (fileCheck.length === 0 || fileCheck[0].user_id !== userId) {
          return res.status(403).json({
            success: false,
            message: '无权访问此文件'
          });
        }

        let whereClause = 'WHERE file_id = ?';
        let params = [fileId];

        if (status === 'used') {
          whereClause += ' AND is_used = 1';
        } else if (status === 'unused') {
          whereClause += ' AND is_used = 0';
        }

        const offset = (page - 1) * limit;

        console.log(`📊 执行查询: WHERE ${whereClause}, LIMIT ${limit}, OFFSET ${offset}`);
        console.log(`📊 查询参数:`, [...params, parseInt(limit), offset]);

        const [uids] = await pool.execute(`
          SELECT uid, is_used, used_time, used_device_id, created_at
          FROM uid_data
          ${whereClause}
          ORDER BY id ASC
          LIMIT ? OFFSET ?
        `, [...params, parseInt(limit) || 20, parseInt(offset) || 0]);

        const [countResult] = await pool.execute(`
          SELECT COUNT(*) as total
          FROM uid_data
          ${whereClause}
        `, params);

        const total = countResult[0].total;

        // 映射字段名以保持前端兼容性
        const mappedUids = uids.map(uid => ({
          uid: uid.uid,
          is_used: uid.is_used,
          used_at: uid.used_time,  // 映射 used_time 到 used_at
          device_id: uid.used_device_id,  // 映射 used_device_id 到 device_id
          assigned_at: uid.created_at  // 使用 created_at 作为 assigned_at
        }));

        res.json({
          success: true,
          data: {
            uids: mappedUids,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total,
              totalPages: Math.ceil(total / limit)
            }
          }
        });
      } else {
        // 数据库不可用时返回内存中的数据
        const file = uidStorage.get(fileId);
        if (!file) {
          return res.status(404).json({
            success: false,
            message: 'UID文件不存在'
          });
        }

        let filteredUids = file.uids;
        if (status === 'used') {
          filteredUids = file.uids.filter(uid => uid.status === 'used');
        } else if (status === 'unused') {
          filteredUids = file.uids.filter(uid => uid.status === 'unused');
        }

        const offset = (page - 1) * limit;
        const paginatedUids = filteredUids.slice(offset, offset + parseInt(limit));

        res.json({
          success: true,
          data: {
            uids: paginatedUids.map(uid => ({
              uid: uid.uid,
              is_used: uid.status === 'used' ? 1 : 0,
              used_at: uid.usedAt || null,
              device_id: uid.deviceId || null
            })),
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: filteredUids.length,
              totalPages: Math.ceil(filteredUids.length / limit)
            }
          }
        });
      }
    } catch (error) {
      console.error('获取UID列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取UID列表失败: ' + error.message
      });
    }
  });

  // 删除UID文件API (原始文件第5279行) - 已添加用户隔离
  app.delete('/api/xiaohongshu/uid-files/:fileId', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { fileId } = req.params;
      const userId = req.currentUserId;

      console.log(`[UID文件删除] 用户${userId}请求删除文件: ${fileId}`);

      // 验证文件所属权
      const hasPermission = await permissionValidator.validateOwnership('uid_file', fileId, userId);
      if (!hasPermission) {
        console.log(`[UID文件删除] 用户${userId}无权删除文件${fileId}`);
        return res.status(403).json({
          success: false,
          message: '无权删除此文件'
        });
      }

      if (pool) {
        // 获取数据库连接进行事务处理
        const connection = await pool.getConnection();

        try {
          // 开始事务
          await connection.beginTransaction();

          // 先获取文件信息，用于删除物理文件（带用户过滤）
          const [fileInfo] = await connection.execute(
            'SELECT file_path, original_name FROM uid_files WHERE id = ? AND user_id = ?',
            [fileId, userId]
          );

          if (fileInfo.length === 0) {
            await connection.rollback();
            connection.release();
            return res.status(404).json({
              success: false,
              message: 'UID文件不存在或无权访问'
            });
          }

          const filePath = fileInfo[0].file_path;
          const originalName = fileInfo[0].original_name;

          // 删除UID数据
          await connection.execute('DELETE FROM uid_data WHERE file_id = ?', [fileId]);

          // 删除文件记录
          const [result] = await connection.execute('DELETE FROM uid_files WHERE id = ?', [fileId]);

          // 提交事务
          await connection.commit();
          connection.release();

          // 删除物理文件
          try {
            if (filePath && fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
              console.log(`物理文件已删除: ${filePath}`);
            }
          } catch (fileError) {
            console.error('删除物理文件失败:', fileError);
            // 不抛出错误，因为数据库记录已经删除成功
          }

          console.log(`UID文件已删除: ${fileId} - ${originalName}`);
        } catch (error) {
          await connection.rollback();
          connection.release();
          throw error;
        }
      } else {
        // 从内存中删除
        if (!uidStorage.has(fileId)) {
          return res.status(404).json({
            success: false,
            message: 'UID文件不存在'
          });
        }
        uidStorage.delete(fileId);
      }

      res.json({
        success: true,
        message: 'UID文件删除成功'
      });

    } catch (error) {
      console.error('删除UID文件失败:', error);
      res.status(500).json({
        success: false,
        message: '删除失败: ' + error.message
      });
    }
  });

  // 重置UID状态API (原始文件第5320行) - 已添加用户隔离
  app.post('/api/xiaohongshu/uid-files/:fileId/reset-status', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { fileId } = req.params;
      const userId = req.currentUserId;

      console.log(`[UID状态重置] 用户${userId}请求重置文件${fileId}的UID状态`);

      // 验证文件所属权
      const hasPermission = await permissionValidator.validateOwnership('uid_file', fileId, userId);
      if (!hasPermission) {
        console.log(`[UID状态重置] 用户${userId}无权重置文件${fileId}`);
        return res.status(403).json({
          success: false,
          message: '无权操作此文件'
        });
      }

      if (pool) {
        const [result] = await dbEnhancer.executeWithUserFilter(`
          UPDATE uid_data
          SET is_used = 0, used_time = NULL, used_device_id = NULL
          WHERE file_id = ?
        `, [fileId], userId);

        console.log(`[UID状态重置] 用户${userId}重置UID状态: ${fileId}, 影响行数: ${result.affectedRows}`);
      } else {
        // 重置内存中的数据
        const file = uidStorage.get(fileId);
        if (!file) {
          return res.status(404).json({
            success: false,
            message: 'UID文件不存在'
          });
        }

        file.uids.forEach(uid => {
          uid.status = 'unused';
          uid.usedAt = null;
          uid.deviceId = null;
        });
        file.usedCount = 0;
      }

      res.json({
        success: true,
        message: 'UID状态重置成功'
      });

    } catch (error) {
      console.error('重置UID状态失败:', error);
      res.status(500).json({
        success: false,
        message: '重置失败: ' + error.message
      });
    }
  });

  // 获取小红书任务状态API (原始文件第5353行) - 已添加用户隔离
  app.get('/api/xiaohongshu/tasks', authenticateToken, userIsolationMiddleware, (req, res) => {
    try {
      const userId = req.currentUserId;

      // 只返回当前用户的任务
      const tasks = Array.from(xiaohongshuActiveTasks.values())
        .filter(task => task.userId === userId)
        .map(task => ({
        id: task.id,
        functionType: task.functionType,
        status: task.status,
        deviceCount: task.deviceIds.length,
        createdAt: task.createdAt,
        startedAt: task.startedAt,
        completedAt: task.completedAt,
        results: task.results.map(r => ({
          deviceId: r.deviceId,
          status: r.status,
          message: r.message
        }))
      }));

      res.json({
        success: true,
        data: {
          activeTasks: tasks.filter(t => t.status === 'running'),
          completedTasks: tasks.filter(t => t.status === 'completed'),
          failedTasks: tasks.filter(t => t.status === 'failed'),
          totalTasks: tasks.length
        }
      });

    } catch (error) {
      console.error('获取任务状态失败:', error);
      res.status(500).json({
        success: false,
        message: '获取任务状态失败: ' + error.message
      });
    }
  });

  // 停止小红书脚本执行API (原始文件第5375行) - 已添加用户隔离
  app.post('/api/xiaohongshu/stop', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { taskId, deviceId, logId } = req.body;
      const userId = req.currentUserId;

      if (!taskId || !deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      console.log(`收到停止请求: taskId=${taskId}, deviceId=${deviceId}`);

      // 如果没有提供logId，使用taskId作为logId（小红书系统中logId等于taskId）
      const actualLogId = logId || taskId;
      console.log(`使用logId: ${actualLogId}`);

      // 查找设备
      let targetDevice = null;
      let targetSocket = null;

      // 查找WebSocket设备
      for (const [socketId, deviceData] of devices) {
        if (deviceData.deviceId === deviceId) {
          targetDevice = deviceData;
          targetSocket = io.sockets.sockets.get(socketId);
          break;
        }
      }

      // 双向.js脚本使用HTTP轮询，所以无论是否有WebSocket连接，都要添加到HTTP命令队列
      console.log(`添加停止命令到HTTP队列: ${deviceId}`);

      // 添加停止命令到待执行队列
      if (!pendingCommands.has(deviceId)) {
        pendingCommands.set(deviceId, []);
      }

      pendingCommands.get(deviceId).push({
        type: 'stop_script',
        taskId: taskId,
        logId: actualLogId,
        reason: '用户手动停止',
        timestamp: Date.now()
      });

      console.log(`停止命令已添加到HTTP设备队列: ${deviceId}`);

      // 立即尝试恢复设备状态为在线（如果设备存在）
      let deviceToUpdate = null;
      for (const [socketId, deviceData] of devices) {
        if (deviceData.deviceId === deviceId) {
          deviceToUpdate = deviceData;
          break;
        }
      }

      if (deviceToUpdate) {
        deviceToUpdate.status = 'online';
        deviceToUpdate.lastActivity = new Date();
        console.log(`设备状态立即恢复为在线: ${deviceId}`);

        // 立即广播设备状态变化
        io.emit('device_status_update', {
          deviceId: deviceId,
          status: 'online',
          lastSeen: new Date().toISOString(),
          message: '脚本停止，设备恢复在线'
        });

        console.log(`已立即广播设备状态更新: ${deviceId} -> online`);
      } else {
        console.log(`未找到设备 ${deviceId}，可能是HTTP设备或已断开连接`);
      }

        // 更新执行状态为停止中
        if (xiaohongshuLogService && actualLogId) {
          try {
            await xiaohongshuLogService.updateExecutionStatus(
              actualLogId,
              'stopping',
              50,
              '正在停止...',
              '用户请求停止脚本执行'
            );

            // 延迟3秒后更新为最终停止状态
            setTimeout(async () => {
              try {
                await xiaohongshuLogService.updateExecutionStatus(
                  actualLogId,
                  'stopped',
                  0,
                  '已停止',
                  '用户手动停止脚本执行'
                );
                console.log(`小红书执行日志已更新为最终停止状态: ${actualLogId}`);

                // 恢复设备状态为在线（重新查找设备，因为targetDevice可能为null）
                let deviceToUpdate = null;
                for (const [socketId, deviceData] of devices) {
                  if (deviceData.deviceId === deviceId) {
                    deviceToUpdate = deviceData;
                    break;
                  }
                }

                if (deviceToUpdate) {
                  deviceToUpdate.status = 'online';
                  deviceToUpdate.lastActivity = new Date();
                  console.log(`设备状态已恢复为在线: ${deviceId}`);
                } else {
                  console.log(`未找到设备 ${deviceId}，可能是HTTP设备或已断开连接`);
                }

                // 通知前端重置脚本执行状态
                io.emit('xiaohongshu_execution_completed', {
                  deviceId: deviceId,
                  taskId: taskId,
                  status: 'stopped',
                  message: '用户手动停止',
                  timestamp: new Date().toISOString()
                });

                // 再次广播设备状态变化（确保状态同步）
                io.emit('device_status_update', {
                  deviceId: deviceId,
                  status: 'online',
                  lastSeen: new Date().toISOString(),
                  message: '脚本已停止，设备恢复在线'
                });

                console.log(`已发送脚本完成通知: ${deviceId}`);

              } catch (delayedError) {
                console.error('延迟更新停止状态失败:', delayedError);
              }
            }, 3000);

          } catch (logError) {
            console.error('更新停止状态失败:', logError);
          }
        }

      res.json({
        success: true,
        message: '停止命令已发送到设备队列'
      });




    } catch (error) {
      console.error('停止脚本执行失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 停止特定设备的小红书脚本执行API (原始文件第5581行) - 已添加用户隔离
  app.post('/api/xiaohongshu/stop-device', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId, reason } = req.body;
      const userId = req.currentUserId;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      console.log(`[用户${userId}] 收到停止设备脚本请求: ${deviceId}, 原因: ${reason}`);

      // 查找设备的所有活跃任务（只查找当前用户的任务）
      const deviceTasks = Array.from(xiaohongshuActiveTasks.values()).filter(task =>
        task.userId === userId && task.deviceIds.includes(deviceId) && task.status === 'running'
      );

      let stoppedCount = 0;
      const results = [];

      for (const task of deviceTasks) {
        try {
          // 查找设备
          let targetDevice = null;
          let targetSocket = null;

          for (const [socketId, deviceData] of devices) {
            if (deviceData.deviceId === deviceId) {
              targetDevice = deviceData;
              targetSocket = io.sockets.sockets.get(socketId);
              break;
            }
          }

          if (targetDevice && targetSocket) {
            // 发送停止命令
            targetSocket.emit('stop_script', {
              taskId: task.id,
              reason: reason || '管理员停止设备任务'
            });

            // 更新任务状态
            const deviceResult = task.results.find(r => r.deviceId === deviceId);
            if (deviceResult) {
              deviceResult.status = 'stopped';
              deviceResult.message = reason || '管理员停止设备任务';
            }

            stoppedCount++;
            results.push({
              taskId: task.id,
              functionType: task.functionType,
              status: 'stopped',
              message: '停止命令已发送'
            });

            console.log(`已向设备 ${deviceId} 发送停止命令，任务: ${task.id}`);
          } else {
            results.push({
              taskId: task.id,
              functionType: task.functionType,
              status: 'offline',
              message: '设备离线，无法发送停止命令'
            });
          }

        } catch (error) {
          console.error(`停止设备任务失败: ${task.id}`, error);
          results.push({
            taskId: task.id,
            functionType: task.functionType,
            status: 'error',
            message: error.message
          });
        }
      }

      res.json({
        success: true,
        message: `设备任务停止完成，成功停止 ${stoppedCount} 个任务`,
        data: {
          deviceId,
          stoppedCount,
          totalTasks: deviceTasks.length,
          results
        }
      });

    } catch (error) {
      console.error('停止设备脚本执行失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 停止所有小红书脚本执行API (原始文件第5677行) - 已添加用户隔离
  app.post('/api/xiaohongshu/stop-all', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { reason } = req.body;
      const userId = req.currentUserId;

      console.log(`[用户${userId}] 收到停止所有脚本请求，原因: ${reason}`);

      // 只停止当前用户的活跃任务
      const activeTasks = Array.from(xiaohongshuActiveTasks.values()).filter(task =>
        task.userId === userId && task.status === 'running'
      );

      let stoppedCount = 0;
      const results = [];

      for (const task of activeTasks) {
        try {
          for (const deviceId of task.deviceIds) {
            // 查找设备
            let targetDevice = null;
            let targetSocket = null;

            for (const [socketId, deviceData] of devices) {
              if (deviceData.deviceId === deviceId) {
                targetDevice = deviceData;
                targetSocket = io.sockets.sockets.get(socketId);
                break;
              }
            }

            if (targetDevice && targetSocket) {
              // 发送停止命令
              targetSocket.emit('stop_script', {
                taskId: task.id,
                reason: reason || '管理员停止所有任务'
              });

              console.log(`已向设备 ${deviceId} 发送停止命令，任务: ${task.id}`);
            }
          }

          // 更新任务状态
          task.status = 'stopped';
          task.completedAt = new Date();
          task.results.forEach(result => {
            if (result.status === 'sent' || result.status === 'running') {
              result.status = 'stopped';
              result.message = reason || '管理员停止所有任务';
            }
          });

          stoppedCount++;
          results.push({
            taskId: task.id,
            functionType: task.functionType,
            deviceCount: task.deviceIds.length,
            status: 'stopped'
          });

        } catch (error) {
          console.error(`停止任务失败: ${task.id}`, error);
          results.push({
            taskId: task.id,
            functionType: task.functionType,
            deviceCount: task.deviceIds.length,
            status: 'error',
            message: error.message
          });
        }
      }

      res.json({
        success: true,
        message: `批量停止完成，成功停止 ${stoppedCount} 个任务`,
        data: {
          stoppedCount,
          totalTasks: activeTasks.length,
          results
        }
      });

    } catch (error) {
      console.error('停止所有脚本执行失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 调试API：查看当前执行状态（无需认证）(原始文件第5794行)
  app.get('/api/xiaohongshu/debug-status', async (req, res) => {
    try {
      const activeTasks = Array.from(xiaohongshuActiveTasks.values());
      const deviceStatuses = new Map();

      // 收集设备状态
      for (const [socketId, deviceData] of devices) {
        deviceStatuses.set(deviceData.deviceId, {
          deviceId: deviceData.deviceId,
          deviceName: deviceData.deviceName,
          status: deviceData.status,
          connectedAt: deviceData.connectedAt,
          lastSeen: deviceData.lastSeen
        });
      }

      const debugInfo = {
        timestamp: new Date().toISOString(),
        activeTasks: activeTasks.map(task => ({
          id: task.id,
          functionType: task.functionType,
          status: task.status,
          deviceCount: task.deviceIds.length,
          createdAt: task.createdAt,
          startedAt: task.startedAt
        })),
        connectedDevices: Array.from(deviceStatuses.values()),
        systemStats: {
          totalActiveTasks: activeTasks.length,
          runningTasks: activeTasks.filter(t => t.status === 'running').length,
          completedTasks: activeTasks.filter(t => t.status === 'completed').length,
          failedTasks: activeTasks.filter(t => t.status === 'failed').length,
          connectedDeviceCount: deviceStatuses.size
        }
      };

      res.json({
        success: true,
        data: debugInfo
      });

    } catch (error) {
      console.error('获取调试状态失败:', error);
      res.status(500).json({
        success: false,
        message: '获取调试状态失败: ' + error.message
      });
    }
  });

  // 按功能类型批量停止小红书脚本执行API (原始文件第5836行) - 已添加用户隔离
  app.post('/api/xiaohongshu/stop-by-function', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { functionType } = req.body;
      const userId = req.currentUserId;

      if (!functionType) {
        return res.status(400).json({
          success: false,
          message: '缺少功能类型参数'
        });
      }

      console.log(`[用户${userId}] 收到按功能类型批量停止请求: functionType=${functionType}`);

      let stoppedCount = 0;
      let updatedCount = 0;
      const stoppedDevices = [];

      // 1. 更新数据库中指定功能类型的正在执行任务
      if (pool) {
        try {
          const updateQuery = `
            UPDATE xiaohongshu_execution_logs
            SET execution_status = 'stopped',
                progress_percentage = 0,
                error_message = '用户批量停止',
                execution_result = '已停止',
                completed_at = NOW(),
                execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
            WHERE (execution_status = 'running' OR execution_status = 'pending')
              AND function_type = ?
              AND user_id = ?
          `;

          const [result] = await pool.execute(updateQuery, [functionType, userId]);
          updatedCount = result.affectedRows;
          console.log(`已更新 ${updatedCount} 个${functionType}执行记录为停止状态`);
        } catch (dbError) {
          console.error('更新数据库记录失败:', dbError);
        }
      }

      // 2. 查找正在执行指定功能的设备并发送停止命令
      const runningDevices = new Set();

      // 从数据库查找正在执行该功能的设备
      if (pool) {
        try {
          console.log(`🔍 [批量停止] 查询正在执行${functionType}功能的设备...`);

          // 查询最近5分钟内创建的记录，不管状态如何
          const [runningTasks] = await pool.execute(`
            SELECT DISTINCT device_id, device_name, execution_status, started_at, task_id
            FROM xiaohongshu_execution_logs
            WHERE function_type = ? AND started_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY started_at DESC
          `, [functionType]);

          console.log(`📊 [批量停止] 数据库查询结果:`, runningTasks);

          runningTasks.forEach(task => {
            runningDevices.add(task.device_id);
            console.log(`📱 [批量停止] 找到正在执行的设备: ${task.device_name} (${task.device_id}), 状态: ${task.execution_status}, 开始时间: ${task.started_at}`);
          });

          console.log(`✅ [批量停止] 总共找到 ${runningDevices.size} 个设备正在执行${functionType}功能:`, Array.from(runningDevices));

        } catch (dbError) {
          console.error('❌ [批量停止] 查询正在执行的任务失败:', dbError);
        }
      }

      // 3. 向正在执行该功能的设备发送停止命令
      console.log(`🔍 [批量停止] 开始查找连接的设备，当前连接设备数: ${devices.size}`);
      console.log(`🔍 [批量停止] 需要停止的设备ID列表:`, Array.from(runningDevices));

      // 直接向所有正在执行的设备发送停止命令，不依赖连接状态
      runningDevices.forEach(deviceId => {
        console.log(`🔍 [批量停止] 处理设备: ${deviceId}`);

        // 首先尝试WebSocket连接
        let deviceFound = false;
        let deviceName = deviceId; // 默认使用deviceId作为名称

        for (const [socketId, device] of devices) {
          if (device.deviceId === deviceId) {
            deviceFound = true;
            deviceName = device.deviceName;

            const deviceSocket = io.sockets.sockets.get(socketId);
            if (deviceSocket) {
              console.log(`📡 [批量停止] WebSocket设备连接存在，发送停止命令: ${deviceId}`);
              deviceSocket.emit('script_command', {
                type: 'stop_script',
                functionType: functionType,
                reason: `用户批量停止${functionType}功能`
              });
              console.log(`✅ [批量停止] 已向WebSocket设备 ${deviceName} (${deviceId}) 发送停止命令`);
            } else {
              console.log(`⚠️ [批量停止] WebSocket连接已断开，尝试HTTP队列: ${deviceId}`);
            }
            break;
          }
        }

        // 对于所有设备（无论是否找到WebSocket连接），都使用HTTP命令队列
        // 因为HTTP设备在执行脚本时通常不保持WebSocket连接
        console.log(`📡 [批量停止] 使用HTTP命令队列发送停止命令: ${deviceId}`);

        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }

        pendingCommands.get(deviceId).push({
          type: 'stop_script',
          functionType: functionType,
          reason: `用户批量停止${functionType}功能`,
          timestamp: new Date()
        });

        stoppedCount++;
        stoppedDevices.push({
          deviceId: deviceId,
          deviceName: deviceName
        });

        console.log(`✅ [批量停止] 已向HTTP设备 ${deviceName} (${deviceId}) 添加停止命令到队列`);
      });

      console.log(`📊 [批量停止] 最终统计: 找到 ${runningDevices.size} 个运行设备，向 ${stoppedCount} 个设备发送停止命令`);

      // 4. 更新设备状态为在线（从忙碌状态恢复）
      console.log(`🔄 [批量停止] 开始更新设备状态为在线...`);
      for (const deviceId of runningDevices) {
        try {
          // 使用已有的updateDeviceStatus函数更新设备状态
          await updateDeviceStatus(deviceId, 'online');
          console.log(`✅ [批量停止] 设备 ${deviceId} 状态已更新为在线`);
        } catch (error) {
          console.error(`❌ [批量停止] 更新设备 ${deviceId} 状态失败:`, error);

          // 备用方案：直接更新数据库和内存
          try {
            // 更新内存中的设备状态
            for (const [socketId, device] of devices) {
              if (device.deviceId === deviceId) {
                device.status = 'online';
                device.lastSeen = new Date();
                console.log(`✅ [批量停止] 内存中设备 ${deviceId} 状态已更新为在线`);
                break;
              }
            }

            // 更新数据库中的设备状态
            if (pool) {
              await pool.execute(`
                UPDATE devices SET status = 'online', last_seen = NOW()
                WHERE device_id = ? AND user_id = ?
              `, [deviceId, userId]);
              console.log(`✅ [批量停止] 数据库中设备 ${deviceId} 状态已更新为在线`);
            }

            // 通知前端设备状态更新
            io.emit('device_status_update', {
              deviceId: deviceId,
              status: 'online',
              lastSeen: new Date()
            });
            console.log(`📡 [批量停止] 已通知前端设备 ${deviceId} 状态更新为在线`);
          } catch (backupError) {
            console.error(`❌ [批量停止] 备用方案也失败: ${deviceId}`, backupError);
          }
        }
      }

      // 5. 通知前端指定功能的任务已停止（使用用户隔离广播）
      broadcastToUserClients(userId, 'xiaohongshu_function_tasks_stopped', {
        functionType: functionType,
        stoppedDevices: stoppedCount,
        updatedTasks: updatedCount,
        deviceList: stoppedDevices,
        timestamp: new Date().toISOString()
      });

      console.log(`📡 [批量停止] 已向用户${userId}的客户端广播功能停止事件`);

      // 6. 批量停止完成后，通知前端强制刷新Vuex状态
      if (stoppedCount > 0) {
        console.log(`📡 [批量停止] 通知前端强制刷新Vuex状态`);

        // 通知前端强制刷新Vuex状态（用户隔离）
        broadcastToUserClients(userId, 'xiaohongshu_force_refresh_vuex', {
          action: 'batchStop',
          functionType: functionType,
          stoppedDevices: stoppedDevices,
          stoppedCount: stoppedCount,
          reason: '批量停止完成，强制刷新状态',
          timestamp: new Date().toISOString()
        });

        // 延迟清理Vuex状态，确保设备状态更新完成
        setTimeout(() => {
          console.log(`🧹 [批量停止] 延迟清理Vuex状态`);
          broadcastToUserClients(userId, 'xiaohongshu_clear_function_state', {
            functionType: functionType,
            reason: '批量停止后清理状态',
            timestamp: new Date().toISOString()
          });
        }, 2000); // 2秒后清理状态
      }

      res.json({
        success: true,
        message: `已批量停止${functionType}功能，向 ${stoppedCount} 个设备发送停止命令，更新了 ${updatedCount} 个数据库记录`,
        data: {
          functionType: functionType,
          stoppedDevices: stoppedCount,
          updatedTasks: updatedCount,
          deviceList: stoppedDevices
        }
      });

    } catch (error) {
      console.error('按功能类型批量停止脚本失败:', error);
      res.status(500).json({
        success: false,
        message: '按功能类型批量停止脚本失败: ' + error.message
      });
    }
  });

  // 获取小红书执行日志列表API (原始文件第6112行) - 已添加用户隔离
  app.get('/api/xiaohongshu/logs', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { page = 1, limit = 20, functionType, status, deviceId } = req.query;
      const userId = req.currentUserId;

      console.log(`[小红书日志] 用户${userId}查询执行日志: page=${page}, limit=${limit}, functionType=${functionType}, status=${status}, deviceId=${deviceId}`);

      if (xiaohongshuLogService) {
        // 使用增强的日志服务，自动添加用户过滤
        const logs = await xiaohongshuLogService.getExecutionLogsWithUserFilter(
          parseInt(page),
          parseInt(limit),
          {
            functionType,
            executionStatus: status,
            deviceId,
            userId  // 添加用户ID过滤
          }
        );

        console.log(`[小红书日志] 用户${userId}查询到${logs.data ? logs.data.length : 0}条日志`);

        res.json({
          success: true,
          data: logs
        });
      } else {
        // 如果日志服务不可用，返回内存中的任务历史（需要按用户过滤）
        const offset = (page - 1) * limit;
        let filteredTasks = xiaohongshuTaskHistory.filter(task => task.userId === userId);

        if (functionType) {
          filteredTasks = filteredTasks.filter(task => task.functionType === functionType);
        }
        if (status) {
          filteredTasks = filteredTasks.filter(task => task.status === status);
        }
        if (deviceId) {
          filteredTasks = filteredTasks.filter(task => task.deviceIds.includes(deviceId));
        }

        const paginatedTasks = filteredTasks.slice(offset, offset + parseInt(limit));

        res.json({
          success: true,
          data: {
            logs: paginatedTasks.map(task => ({
              id: task.id,
              functionType: task.functionType,
              executionStatus: task.status,
              deviceId: task.deviceIds[0] || null,
              deviceName: task.deviceName || '未知设备',
              startedAt: task.startedAt,
              completedAt: task.completedAt,
              progressPercentage: task.status === 'completed' ? 100 : 0,
              statusMessage: task.status,
              configData: JSON.stringify(task.config)
            })),
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: filteredTasks.length,
              totalPages: Math.ceil(filteredTasks.length / limit)
            }
          }
        });
      }

    } catch (error) {
      console.error('获取执行日志失败:', error);
      res.status(500).json({
        success: false,
        message: '获取执行日志失败: ' + error.message
      });
    }
  });

  // 获取小红书执行日志详情API (原始文件第6150行) - 已添加用户隔离
  app.get('/api/xiaohongshu/logs/:taskId', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { taskId } = req.params;
      const userId = req.currentUserId;

      if (xiaohongshuLogService) {
        const logDetail = await xiaohongshuLogService.getExecutionLogDetail(taskId);

        if (!logDetail) {
          return res.status(404).json({
            success: false,
            message: '执行日志不存在'
          });
        }

        // 验证日志所属权
        if (logDetail.user_id !== userId) {
          return res.status(403).json({
            success: false,
            message: '无权访问此执行日志'
          });
        }

        res.json({
          success: true,
          data: logDetail
        });
      } else {
        // 从内存中查找任务详情（只查找当前用户的任务）
        const task = xiaohongshuActiveTasks.get(taskId) ||
                     xiaohongshuTaskHistory.find(t => t.id === taskId && t.userId === userId);

        if (!task) {
          return res.status(404).json({
            success: false,
            message: '任务不存在'
          });
        }

        res.json({
          success: true,
          data: {
            id: task.id,
            functionType: task.functionType,
            executionStatus: task.status,
            deviceId: task.deviceIds[0] || null,
            deviceName: task.deviceName || '未知设备',
            startedAt: task.startedAt,
            completedAt: task.completedAt,
            progressPercentage: task.status === 'completed' ? 100 : 0,
            statusMessage: task.status,
            configData: JSON.stringify(task.config),
            results: task.results
          }
        });
      }

    } catch (error) {
      console.error('获取执行日志详情失败:', error);
      res.status(500).json({
        success: false,
        message: '获取执行日志详情失败: ' + error.message
      });
    }
  });

  // 清空小红书执行日志API (原始文件第6183行) - 已添加用户隔离
  app.delete('/api/xiaohongshu/logs', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;

      if (xiaohongshuLogService) {
        await xiaohongshuLogService.clearExecutionLogs(userId);
        console.log(`[用户${userId}] 小红书执行日志已清空`);
      } else {
        // 清空内存中的任务历史
        xiaohongshuTaskHistory = [];
        xiaohongshuActiveTasks.clear();
        console.log('内存中的小红书任务历史已清空');
      }

      res.json({
        success: true,
        message: '执行日志已清空'
      });

    } catch (error) {
      console.error('清空执行日志失败:', error);
      res.status(500).json({
        success: false,
        message: '清空执行日志失败: ' + error.message
      });
    }
  });

  // 上传UID文件API (原始文件第6214行) - 已添加用户隔离
  app.post('/api/xiaohongshu/upload-uids', authenticateToken, userIsolationMiddleware, upload.single('file'), async (req, res) => {
    try {
      const userId = req.currentUserId;

      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '没有上传文件'
        });
      }

      const filePath = req.file.path;
      const fileName = req.file.filename;
      // 修复文件名编码问题
      const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');

      console.log('收到UID文件上传:', {
        fileName,
        originalName,
        filePath,
        size: req.file.size
      });

      // 读取文件内容
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const lines = fileContent.split('\n').filter(line => line.trim());

      console.log(`文件包含 ${lines.length} 行数据`);

      // 解析UID数据
      const uids = [];
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
          uids.push({
            uid: line,
            status: 'unused',
            lineNumber: i + 1
          });
        }
      }

      // 存储到内存（实际应用中应存储到数据库）
      const fileId = Date.now().toString();
      uidStorage.set(fileId, {
        id: fileId,
        fileName: originalName,
        uploadedAt: new Date(),
        totalCount: uids.length,
        usedCount: 0,
        uids: uids
      });

      console.log(`UID文件已保存，ID: ${fileId}, 包含 ${uids.length} 个UID`);

      res.json({
        success: true,
        message: 'UID文件上传成功',
        data: {
          fileId,
          fileName: originalName,
          totalCount: uids.length,
          usedCount: 0
        }
      });

    } catch (error) {
      console.error('UID文件上传失败:', error);
      res.status(500).json({
        success: false,
        message: '上传失败: ' + error.message
      });
    }
  });

  // 记录UID私信结果API (原始文件第6274行) - 连接码模式，无需token
  app.post('/api/xiaohongshu/record-uid-message', async (req, res) => {
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null);
    console.log(`📥 收到UID私信结果上报请求`);
    console.log('请求来源IP:', clientIP);
    console.log('请求体:', JSON.stringify(req.body, null, 2));

    try {
      const { taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage, fileId } = req.body;

      // 从设备ID获取用户ID
      let userId = null;
      if (pool && deviceId) {
        try {
          const [deviceRows] = await pool.execute(
            'SELECT user_id FROM devices WHERE device_id = ?',
            [deviceId]
          );
          if (deviceRows.length > 0) {
            userId = deviceRows[0].user_id;
            console.log(`从设备${deviceId}获取到用户ID: ${userId}`);
          } else {
            console.log(`设备${deviceId}未找到对应的用户`);
          }
        } catch (dbError) {
          console.error('查询设备用户失败:', dbError);
        }
      }

      // 内存存储记录（保持原有逻辑）
      const messageRecord = {
        id: Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        taskId,
        deviceId,
        deviceName,
        uid,
        messageContent,
        sendStatus,
        errorMessage,
        sendTime: new Date().toISOString()
      };

      uidMessageStorage.set(messageRecord.id, messageRecord);

      // 数据库存储记录（新增逻辑）
      if (pool) {
        try {
          const connection = await pool.getConnection();

          try {
            // 根据taskId判断功能类型
            let tableName;
            let insertQuery;
            let insertParams;

            // 检查taskId中是否包含uidFileMessage，如果包含则写入文件上传表，否则写入手动输入表
            if (taskId && taskId.includes('uidFileMessage')) {
              tableName = 'xiaohongshu_file_uid_messages';
              insertQuery = `
                INSERT INTO ${tableName}
                (task_id, device_id, device_name, uid, message_content, send_status, error_message, file_id, user_id, send_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
              `;
              insertParams = [taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage, fileId || null, userId];
            } else {
              tableName = 'xiaohongshu_manual_uid_messages';
              insertQuery = `
                INSERT INTO ${tableName}
                (task_id, device_id, device_name, uid, message_content, send_status, error_message, user_id, send_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
              `;
              insertParams = [taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage, userId];
            }

            await connection.execute(insertQuery, insertParams);
            console.log(`UID私信记录已保存到数据库 ${tableName}: 设备${deviceId}, UID${uid}, 状态${sendStatus}`);

            // 只有在私信成功时才标记UID为已使用并记录设备信息
            if (uid && taskId && sendStatus === 'success') {
              try {
                // 首先尝试使用任务ID匹配更新
                let [uidUpdateResult] = await connection.execute(
                  'UPDATE uid_data SET is_used = 1, used_time = NOW(), used_device_id = ?, used_device_name = ?, task_id = ? WHERE uid = ? AND (task_id = ? OR task_id IS NULL)',
                  [deviceId, deviceName, taskId, uid, taskId]
                );

                if (uidUpdateResult.affectedRows === 0) {
                  // 如果没有匹配到，尝试不使用任务ID匹配
                  [uidUpdateResult] = await connection.execute(
                    'UPDATE uid_data SET is_used = 1, used_time = NOW(), used_device_id = ?, used_device_name = ?, task_id = ? WHERE uid = ? AND is_used = 0',
                    [deviceId, deviceName, taskId, uid]
                  );
                }

                if (uidUpdateResult.affectedRows > 0) {
                  console.log(`✅ UID ${uid} 已标记为已使用，设备: ${deviceId}`);
                } else {
                  console.warn(`⚠️ UID ${uid} 更新失败，可能已被使用或不存在`);
                }
              } catch (uidUpdateError) {
                console.error(`更新UID ${uid} 状态失败:`, uidUpdateError);
              }
            } else if (uid && taskId && sendStatus === 'failed') {
              console.log(`❌ UID ${uid} 私信失败，保持未使用状态，不记录设备信息`);
            }

          } finally {
            connection.release();
          }
        } catch (dbError) {
          console.error('保存UID私信记录到数据库失败:', dbError);
          // 数据库错误不影响响应，继续返回成功
        }
      }

      res.json({
        success: true,
        message: '私信记录保存成功'
      });

    } catch (error) {
      console.error('记录UID私信结果失败:', error);
      res.status(500).json({
        success: false,
        message: '记录失败: ' + error.message
      });
    }
  });

  // 查询UID状态API (原始文件第6386行) - 已添加用户隔离
  app.get('/api/xiaohongshu/uid-status/:uid', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { uid } = req.params;
      const userId = req.currentUserId;

      console.log(`[用户${userId}] 查询UID状态: ${uid}`);

      // 在内存存储中查找UID
      let uidFound = null;
      let fileInfo = null;

      for (const [fileId, file] of uidStorage) {
        const uidData = file.uids.find(u => u.uid === uid);
        if (uidData) {
          uidFound = uidData;
          fileInfo = {
            fileId,
            fileName: file.fileName
          };
          break;
        }
      }

      if (!uidFound) {
        return res.status(404).json({
          success: false,
          message: 'UID不存在'
        });
      }

      // 查找私信记录
      const messageRecords = Array.from(uidMessageStorage.values())
        .filter(record => record.uid === uid)
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      res.json({
        success: true,
        data: {
          uid,
          status: uidFound.status,
          usedAt: uidFound.usedAt,
          deviceId: uidFound.deviceId,
          file: fileInfo,
          messageRecords: messageRecords.map(record => ({
            id: record.id,
            deviceId: record.deviceId,
            success: record.success,
            message: record.message,
            timestamp: record.timestamp,
            clientIP: record.clientIP
          }))
        }
      });

    } catch (error) {
      console.error('查询UID状态失败:', error);
      res.status(500).json({
        success: false,
        message: '查询失败: ' + error.message
      });
    }
  });

  // 接收脚本执行完成通知API (原始文件第6456行) - 连接码模式，无需token
  app.post('/api/xiaohongshu/execution-complete', async (req, res) => {
    try {
      const { deviceId, taskId, success, message, results, timestamp } = req.body;

      console.log(`收到脚本执行完成通知: 设备=${deviceId}, 任务=${taskId}, 成功=${success}`);

      if (!deviceId || !taskId) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 验证设备是否存在
      if (pool) {
        try {
          const [existingDevices] = await pool.execute(
            'SELECT user_id, device_name FROM devices WHERE device_id = ?',
            [deviceId]
          );

          if (existingDevices.length === 0) {
            console.log(`设备${deviceId}未注册`);
            return res.status(404).json({
              success: false,
              message: '设备未注册'
            });
          }
        } catch (dbError) {
          console.error('验证设备存在性失败:', dbError);
          return res.status(500).json({
            success: false,
            message: '数据库查询失败'
          });
        }
      }

      // 更新任务状态
      const task = xiaohongshuActiveTasks.get(taskId);
      if (task) {
        task.status = success ? 'completed' : 'failed';
        task.completedAt = new Date();

        // 更新设备结果
        const deviceResult = task.results.find(r => r.deviceId === deviceId);
        if (deviceResult) {
          deviceResult.status = success ? 'completed' : 'failed';
          deviceResult.message = message || (success ? '执行成功' : '执行失败');
          deviceResult.results = results;
        }

        // 如果所有设备都完成了，将任务移到历史记录
        const allCompleted = task.results.every(r =>
          r.status === 'completed' || r.status === 'failed' || r.status === 'stopped'
        );

        if (allCompleted) {
          xiaohongshuTaskHistory.push(task);
          xiaohongshuActiveTasks.delete(taskId);
          console.log(`任务 ${taskId} 已完成，移至历史记录`);
        }
      }

      // 通知前端执行完成
      io.emit('xiaohongshu_execution_completed', {
        deviceId,
        taskId,
        success,
        message,
        results,
        timestamp: timestamp || new Date().toISOString()
      });

      res.json({
        success: true,
        message: '执行完成通知已接收'
      });

    } catch (error) {
      console.error('处理执行完成通知失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 上传视频文件API（支持批量上传，带重复检测）(原始文件第6705行) - 已添加用户隔离
  app.post('/api/xiaohongshu/upload-video-files', authenticateToken, userIsolationMiddleware, (req, res, next) => {
    const userId = req.currentUserId;

    console.log(`[视频文件上传] 用户${userId}开始处理视频文件上传请求`);
    console.log('请求头:', req.headers['content-type']);
    console.log('请求体大小:', req.headers['content-length']);

    // 使用videoUpload处理文件上传
    videoUpload.array('videos', 1000)(req, res, (err) => {
      if (err) {
        console.error(`[视频文件上传] 用户${userId}上传失败:`, err);

        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              message: '文件大小超过限制（最大2GB）'
            });
          } else if (err.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
              success: false,
              message: '文件数量超过限制（最大1000个文件）'
            });
          } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
              success: false,
              message: `不支持的文件字段名: ${err.field}，应该使用 'videos'`
            });
          }
        }

        return res.status(400).json({
          success: false,
          message: '文件上传错误: ' + err.message
        });
      }

      console.log(`[视频文件上传] 用户${userId}Multer处理完成，文件数量:`, req.files ? req.files.length : 0);
      next();
    });
  }, async (req, res) => {
    try {
      const userId = req.currentUserId;

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ success: false, message: '没有上传视频文件' });
      }

      const uploadedBy = req.user ? req.user.username : 'admin';
      const uploadResults = [];
      const duplicates = [];

      // 获取数据库连接
      const connection = await pool.getConnection();

      try {
        await connection.beginTransaction();

        for (const file of req.files) {
          const filePath = file.path;
          const fileName = file.filename;
          const originalName = file.originalname;
          const fileSize = file.size;
          const videoFormat = path.extname(originalName).toLowerCase().substring(1);

          // 计算文件哈希值
          const fileHash = await calculateFileHash(filePath);

          // 检查是否已存在相同的文件（基于文件哈希值和大小）
          const [existingFiles] = await connection.execute(
            `SELECT id, original_name, upload_time FROM xiaohongshu_video_files
             WHERE file_size = ? AND status = 'active'`,
            [fileSize]
          );

          let isDuplicate = false;
          let duplicateInfo = null;

          // 对于相同大小的文件，进一步检查哈希值
          for (const existingFile of existingFiles) {
            try {
              const [fileDetails] = await connection.execute(
                `SELECT file_hash FROM xiaohongshu_video_files WHERE id = ?`,
                [existingFile.id]
              );

              if (fileDetails.length > 0 && fileDetails[0].file_hash === fileHash) {
                isDuplicate = true;
                duplicateInfo = {
                  id: existingFile.id,
                  originalName: existingFile.original_name,
                  uploadTime: existingFile.upload_time
                };
                break;
              }
            } catch (hashError) {
              // 如果file_hash字段不存在，跳过哈希检查，只基于文件名和大小检查
              if (hashError.code === 'ER_BAD_FIELD_ERROR') {
                console.log('数据库表不支持file_hash字段，跳过哈希检查');
                // 基于文件名和大小的简单重复检查
                if (existingFile.original_name === originalName) {
                  isDuplicate = true;
                  duplicateInfo = {
                    id: existingFile.id,
                    originalName: existingFile.original_name,
                    uploadTime: existingFile.upload_time
                  };
                  break;
                }
              } else {
                throw hashError;
              }
            }
          }

          if (isDuplicate) {
            duplicates.push({
              originalName: originalName,
              existingId: duplicateInfo.id,
              existingName: duplicateInfo.originalName,
              existingUploadTime: duplicateInfo.uploadTime,
              message: '文件已存在（基于文件内容检测）'
            });

            // 删除重复的上传文件
            try {
              fs.unlinkSync(filePath);
              console.log(`删除重复文件: ${filePath}`);
            } catch (deleteError) {
              console.warn(`删除重复文件失败: ${deleteError.message}`);
            }

            continue;
          }

          // 获取视频信息和生成缩略图
          let videoInfo = { duration: 0, resolution: 'unknown' };
          let thumbnailPath = null;

          try {
            videoInfo = await getVideoInfo(filePath);
            thumbnailPath = await generateThumbnail(filePath, fileName);
            console.log(`视频信息获取成功: ${originalName}`, videoInfo);
          } catch (videoError) {
            console.warn(`处理视频 ${originalName} 时出错:`, videoError.message);
            // 继续处理，但不包含视频信息和缩略图
          }

          // 插入视频文件记录，包含视频信息（自动添加user_id）
          try {
            // 使用数据库增强器插入，自动添加user_id
            const [fileResult] = await dbEnhancer.insertWithUserId('xiaohongshu_video_files', {
              file_name: fileName,
              original_name: originalName,
              file_path: filePath,
              file_size: fileSize,
              video_format: videoFormat,
              uploaded_by: req.user.username || 'unknown',
              description: req.body.description || '',
              tags: req.body.tags || '',
              file_hash: fileHash,
              video_duration: Math.round(videoInfo.duration),
              video_resolution: videoInfo.resolution,
              thumbnail_path: thumbnailPath
            }, userId);

            uploadResults.push({
              videoId: fileResult.insertId,
              fileName: originalName,
              fileSize: fileSize,
              videoFormat: videoFormat,
              videoDuration: Math.round(videoInfo.duration),
              videoResolution: videoInfo.resolution,
              thumbnailPath: thumbnailPath,
              uploadTime: new Date().toISOString()
            });

          } catch (insertError) {
            console.error('插入视频记录失败:', insertError.message);

            // 如果是字段不存在的错误，尝试不包含file_hash的插入
            if (insertError.message.includes('file_hash') || insertError.code === 'ER_BAD_FIELD_ERROR') {
              console.log('尝试不包含file_hash字段的插入...');

              try {
                // 使用数据库增强器插入，自动添加user_id（不包含file_hash字段）
                const [fileResult] = await dbEnhancer.insertWithUserId('xiaohongshu_video_files', {
                  file_name: fileName,
                  original_name: originalName,
                  file_path: filePath,
                  file_size: fileSize,
                  video_format: videoFormat,
                  uploaded_by: req.user.username || 'unknown',
                  description: req.body.description || '',
                  tags: req.body.tags || '',
                  video_duration: Math.round(videoInfo.duration),
                  video_resolution: videoInfo.resolution,
                  thumbnail_path: thumbnailPath
                }, userId);

                uploadResults.push({
                  videoId: fileResult.insertId,
                  fileName: originalName,
                  fileSize: fileSize,
                  videoFormat: videoFormat,
                  videoDuration: Math.round(videoInfo.duration),
                  videoResolution: videoInfo.resolution,
                  thumbnailPath: thumbnailPath,
                  uploadTime: new Date().toISOString()
                });
              } catch (insertError2) {
                console.error('第二次插入也失败:', insertError2.message);

                // 如果video_duration等字段也不存在，使用最基本的字段插入
                if (insertError2.code === 'ER_BAD_FIELD_ERROR') {
                  console.log('尝试使用基本字段插入...');
                  // 使用数据库增强器插入，自动添加user_id（只使用基本字段）
                  const [fileResult] = await dbEnhancer.insertWithUserId('xiaohongshu_video_files', {
                    file_name: fileName,
                    original_name: originalName,
                    file_path: filePath,
                    file_size: fileSize,
                    video_format: videoFormat,
                    uploaded_by: req.user.username || 'unknown',
                    description: req.body.description || '',
                    tags: req.body.tags || ''
                  }, userId);

                  uploadResults.push({
                    videoId: fileResult.insertId,
                    fileName: originalName,
                    fileSize: fileSize,
                    videoFormat: videoFormat,
                    videoDuration: Math.round(videoInfo.duration),
                    videoResolution: videoInfo.resolution,
                    thumbnailPath: thumbnailPath,
                    uploadTime: new Date().toISOString()
                  });
                } else {
                  throw insertError2;
                }
              }
            } else {
              throw insertError;
            }
          }
        }

        await connection.commit();
        connection.release();

        console.log(`视频上传处理完成: 成功=${uploadResults.length}, 重复=${duplicates.length}`);

        // 构建响应消息（与原始server.js保持一致）
        let message = '';
        if (uploadResults.length > 0 && duplicates.length > 0) {
          message = `成功上传 ${uploadResults.length} 个视频文件，跳过 ${duplicates.length} 个重复文件`;
        } else if (uploadResults.length > 0) {
          message = `成功上传 ${uploadResults.length} 个视频文件`;
        } else {
          message = `所有 ${duplicates.length} 个文件都是重复文件，已跳过上传`;
        }

        res.json({
          success: true,
          message: message,
          data: {
            uploadCount: uploadResults.length,
            duplicateCount: duplicates.length,
            videos: uploadResults,
            duplicates: duplicates
          }
        });

      } catch (error) {
        await connection.rollback();
        connection.release();
        console.error('视频上传处理失败:', error);
        res.status(500).json({
          success: false,
          message: '视频上传处理失败: ' + error.message
        });
      }

    } catch (error) {
      console.error('视频上传失败:', error);
      res.status(500).json({
        success: false,
        message: '视频上传失败: ' + error.message
      });
    }
  });

  // 删除单个视频API (原始文件第6948行)
  app.delete('/api/xiaohongshu/videos/:id', authenticateToken, async (req, res) => {
    try {
      const { id } = req.params;

      const video = videoStorage.get(id);
      if (!video) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      // 删除文件
      try {
        if (fs.existsSync(video.filePath)) {
          fs.unlinkSync(video.filePath);
          console.log(`视频文件已删除: ${video.filePath}`);
        }
      } catch (fileError) {
        console.error('删除视频文件失败:', fileError);
      }

      // 从存储中移除
      videoStorage.delete(id);

      console.log(`视频已删除: ${id} - ${video.originalName}`);

      res.json({
        success: true,
        message: '视频删除成功'
      });

    } catch (error) {
      console.error('删除视频失败:', error);
      res.status(500).json({
        success: false,
        message: '删除失败: ' + error.message
      });
    }
  });

  // 下载视频文件API - 支持分块传输和断点续传 (原始文件第7016行) - 已添加用户隔离
  app.get('/api/xiaohongshu/download-video/:id', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.currentUserId;
      console.log(`📥 [视频下载API] 用户${userId}请求下载视频，视频ID: ${id}`);

      // 从数据库中查询视频信息（按用户过滤）
      const [videos] = await pool.execute(
        'SELECT * FROM xiaohongshu_video_files WHERE id = ? AND status = "active" AND user_id = ?',
        [id, userId]
      );

      if (videos.length === 0) {
        console.log(`❌ [视频下载API] 视频不存在，ID: ${id}`);
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      const video = videos[0];
      const filePath = video.file_path;
      console.log(`📁 [视频下载API] 视频文件路径: ${filePath}`);

      if (!fs.existsSync(filePath)) {
        console.log(`❌ [视频下载API] 视频文件不存在: ${filePath}`);
        return res.status(404).json({
          success: false,
          message: '视频文件不存在'
        });
      }

      const stat = fs.statSync(filePath);
      const fileSize = stat.size;
      const range = req.headers.range;

      if (range) {
        // 支持断点续传
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunksize = (end - start) + 1;

        const file = fs.createReadStream(filePath, { start, end });

        const head = {
          'Content-Range': `bytes ${start}-${end}/${fileSize}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunksize,
          'Content-Type': video.video_format === 'mp4' ? 'video/mp4' : 'video/mp4',
          'Content-Disposition': `attachment; filename="${encodeURIComponent(video.original_name)}"`
        };

        res.writeHead(206, head);
        file.pipe(res);
      } else {
        // 完整文件下载
        const head = {
          'Content-Length': fileSize,
          'Content-Type': video.video_format === 'mp4' ? 'video/mp4' : 'video/mp4',
          'Content-Disposition': `attachment; filename="${encodeURIComponent(video.original_name)}"`
        };

        res.writeHead(200, head);
        fs.createReadStream(filePath).pipe(res);
      }

      console.log(`✅ [视频下载API] 视频下载成功: ${id} - ${video.original_name}`);

    } catch (error) {
      console.error('视频下载失败:', error);
      res.status(500).json({
        success: false,
        message: '下载失败: ' + error.message
      });
    }
  });

  // 传输视频到手机设备（独立功能）API (原始文件第7096行)
  app.post('/api/xiaohongshu/transfer-videos', authenticateToken, async (req, res) => {
    console.log('🚀🚀🚀 [视频传输API] ===== 传输视频API被调用 ===== 🚀🚀🚀');

    try {
      const { deviceIds, videoIds, transferMode = 'copy' } = req.body;

      console.log('传输参数:', {
        deviceIds,
        videoIds,
        transferMode,
        deviceCount: deviceIds ? deviceIds.length : 0,
        videoCount: videoIds ? videoIds.length : 0
      });

      if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID列表'
        });
      }

      if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '缺少视频ID列表'
        });
      }

      // 验证视频是否存在
      const videos = [];
      for (const videoId of videoIds) {
        const video = videoStorage.get(videoId);
        if (!video) {
          return res.status(404).json({
            success: false,
            message: `视频不存在: ${videoId}`
          });
        }
        videos.push(video);
      }

      // 创建传输记录
      const transferId = Date.now().toString();
      const transferRecord = {
        id: transferId,
        deviceIds,
        videoIds,
        videos: videos.map(v => ({
          id: v.id,
          originalName: v.originalName,
          size: v.size
        })),
        transferMode,
        status: 'pending',
        createdAt: new Date(),
        startedAt: null,
        completedAt: null,
        progress: 0,
        results: []
      };

      videoTransferRecords.set(transferId, transferRecord);

      // 模拟传输过程（实际应用中应该发送到设备）
      transferRecord.status = 'transferring';
      transferRecord.startedAt = new Date();

      let successCount = 0;
      let failCount = 0;

      for (const deviceId of deviceIds) {
        try {
          // 查找设备
          let targetDevice = null;
          for (const [socketId, deviceData] of devices) {
            if (deviceData.deviceId === deviceId) {
              targetDevice = deviceData;
              break;
            }
          }

          if (targetDevice) {
            // 记录传输结果
            transferRecord.results.push({
              deviceId,
              status: 'success',
              message: '传输成功',
              transferredVideos: videos.length
            });
            successCount++;
          } else {
            transferRecord.results.push({
              deviceId,
              status: 'failed',
              message: '设备离线'
            });
            failCount++;
          }

        } catch (error) {
          transferRecord.results.push({
            deviceId,
            status: 'failed',
            message: error.message
          });
          failCount++;
        }
      }

      transferRecord.status = 'completed';
      transferRecord.completedAt = new Date();
      transferRecord.progress = 100;

      console.log(`视频传输完成: 成功=${successCount}, 失败=${failCount}`);

      res.json({
        success: true,
        message: `视频传输完成，成功: ${successCount}个设备，失败: ${failCount}个设备`,
        data: {
          transferId,
          successCount,
          failCount,
          totalDevices: deviceIds.length,
          totalVideos: videoIds.length,
          results: transferRecord.results
        }
      });

    } catch (error) {
      console.error('视频传输失败:', error);
      res.status(500).json({
        success: false,
        message: '传输失败: ' + error.message
      });
    }
  });

  // 上报视频传输进度（实时进度）API (原始文件第7265行) - 改为设备认证模式
  app.post('/api/xiaohongshu/report-transfer-progress', authenticateDevice(pool), async (req, res) => {
    try {
      // 兼容两种参数格式：新格式和脚本格式
      const {
        transferId, taskId, deviceId, videoId, videoName, progress,
        transferredBytes, totalBytes, transferSpeed, status,
        message, currentVideo
      } = req.body;

      // 使用taskId或transferId作为任务ID
      const actualTaskId = taskId || transferId;
      const actualVideoName = videoName || currentVideo || '未知视频';

      console.log('📊 [传输进度] 收到进度上报:', {
        taskId: actualTaskId, deviceId, videoId,
        videoName: actualVideoName,
        originalVideoName: videoName,
        currentVideo: currentVideo,
        progress, status
      });

      if (!actualTaskId || !deviceId || !videoId) {
        console.log(`❌ [传输进度] 缺少必要参数`);
        return res.status(400).json({
          success: false,
          message: '缺少必要参数: taskId, deviceId, videoId'
        });
      }

      // 更新数据库中的传输记录
      if (pool) {
        try {
          // 查找现有传输记录
          const [existingRecords] = await pool.execute(`
            SELECT id, status FROM xiaohongshu_video_transfers
            WHERE task_id = ? AND device_id = ? AND video_id = ?
            ORDER BY id DESC LIMIT 1
          `, [actualTaskId, deviceId, videoId]);

          if (existingRecords.length > 0) {
            const recordId = existingRecords[0].id;
            console.log('📝 [传输进度] 找到现有记录，ID:', recordId, '当前状态:', existingRecords[0].status);

            // 更新现有记录的进度和状态
            const dbStatus = (status === 'downloading' || status === 'starting') ? 'transferring' : (status || 'transferring');
            await pool.execute(`
              UPDATE xiaohongshu_video_transfers
              SET transfer_progress = ?, status = ?, file_size = ?, transfer_speed = ?, video_filename = ?,
                  completed_time = CASE WHEN ? = 'completed' THEN NOW() ELSE completed_time END
              WHERE id = ?
            `, [Math.floor(progress), dbStatus, totalBytes || 0, transferSpeed || 0, actualVideoName, dbStatus, recordId]);

            console.log('✅ [传输进度] 已更新传输记录，进度:', progress + '%', '状态:', status);
          } else {
            // 创建新的传输记录
            const dbStatus = (status === 'downloading' || status === 'starting') ? 'transferring' : (status || 'transferring');
            await pool.execute(`
              INSERT INTO xiaohongshu_video_transfers
              (video_id, device_id, device_name, transfer_type, task_id, status, transfer_progress,
               file_size, transfer_speed, video_filename, transfer_time)
              VALUES (?, ?, ?, 'script_execution', ?, ?, ?, ?, ?, ?, NOW())
            `, [videoId, deviceId, deviceId, actualTaskId, dbStatus, Math.floor(progress),
                totalBytes || 0, transferSpeed || 0, actualVideoName]);

            console.log('✅ [传输进度] 已创建新传输记录，进度:', progress + '%', '状态:', status);
          }
        } catch (dbError) {
          console.error('❌ [传输进度] 更新数据库失败:', dbError);
          // 数据库更新失败不影响进度广播
        }
      }

      // 通知前端进度更新
      const progressData = {
        type: 'video_transfer_progress',
        taskId: actualTaskId,
        deviceId,
        videoId: videoId,
        videoName: actualVideoName,
        progress: Math.floor(progress) || 0,
        transferredBytes: transferredBytes || 0,
        totalBytes: totalBytes || 0,
        transferSpeed: transferSpeed || 0,
        status: status || 'transferring',
        message: message || '',
        timestamp: new Date().toISOString()
      };

      const { userId, username } = req.device; // 来自设备认证中间件
      console.log(`📊 [传输进度] 用户${userId}设备${deviceId}广播进度数据:`, progressData);

      // 只向设备所属用户的客户端发送传输进度
      broadcastToUserClients(userId, 'video_transfer_progress', progressData);
      console.log(`✅ [传输进度] 已广播给用户${userId}的客户端`);

      res.json({
        success: true,
        message: '传输进度已更新'
      });

    } catch (error) {
      console.error('上报传输进度失败:', error);
      res.status(500).json({
        success: false,
        message: '上报失败: ' + error.message
      });
    }
  });

  // 查询视频传输记录API (原始文件第7370行)
  app.get('/api/xiaohongshu/transfer-records', authenticateToken, async (req, res) => {
    try {
      const { page = 1, limit = 20, status, deviceId } = req.query;

      let records = Array.from(videoTransferRecords.values());

      // 过滤条件
      if (status) {
        records = records.filter(record => record.status === status);
      }
      if (deviceId) {
        records = records.filter(record => record.deviceIds.includes(deviceId));
      }

      // 排序（最新的在前）
      records.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // 分页
      const offset = (page - 1) * limit;
      const paginatedRecords = records.slice(offset, offset + parseInt(limit));

      res.json({
        success: true,
        data: {
          records: paginatedRecords.map(record => ({
            id: record.id,
            deviceCount: record.deviceIds.length,
            videoCount: record.videoIds.length,
            status: record.status,
            progress: record.progress,
            createdAt: record.createdAt,
            startedAt: record.startedAt,
            completedAt: record.completedAt,
            results: record.results.map(r => ({
              deviceId: r.deviceId,
              status: r.status,
              progress: r.progress,
              message: r.message
            }))
          })),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: records.length,
            totalPages: Math.ceil(records.length / limit)
          }
        }
      });

    } catch (error) {
      console.error('查询传输记录失败:', error);
      res.status(500).json({
        success: false,
        message: '查询失败: ' + error.message
      });
    }
  });

  // 更新视频传输状态API - 修复为使用数据库存储（内部调用，不需要认证）
  app.post('/api/xiaohongshu/update-transfer-status', async (req, res) => {
    try {
      const { taskId, deviceId, status, message, error, videoId, progress, errorMessage } = req.body;
      console.log(`📊 [传输状态更新] 收到状态更新请求:`, req.body);

      if (!taskId || !deviceId || !status) {
        console.log(`❌ [传输状态更新] 缺少必要参数:`, { taskId, deviceId, status });
        return res.status(400).json({
          success: false,
          message: '缺少必要参数: taskId, deviceId, status'
        });
      }

      // 兼容脚本发送的参数名
      const finalError = error || errorMessage;
      const finalMessage = message || `状态: ${status}`;
      const finalProgress = progress;

      if (!pool) {
        console.log(`❌ [传输状态更新] 数据库连接不可用`);
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      // 计算进度百分比
      let progressPercentage = 0;
      if (finalProgress !== undefined && finalProgress !== null) {
        // 优先使用脚本直接传递的进度值
        progressPercentage = parseInt(finalProgress) || 0;
      } else if (status === 'completed') {
        progressPercentage = 100;
      } else if (status === 'failed') {
        progressPercentage = 0;
      } else if (status === 'transferring') {
        // 对于传输中状态，可以从message中提取进度信息
        const progressMatch = finalMessage && finalMessage.match(/(\d+)%/);
        if (progressMatch) {
          progressPercentage = parseInt(progressMatch[1]);
        }
      }

      // 更新数据库中的传输记录（使用正确的字段名）
      const updateQuery = `
        UPDATE xiaohongshu_video_transfers
        SET status = ?, transfer_progress = ?, error_message = ?,
            completed_time = CASE WHEN ? = 'completed' THEN NOW() ELSE completed_time END
        WHERE task_id = ? AND device_id = ?
      `;

      const [updateResult] = await pool.execute(updateQuery, [
        status,
        progressPercentage,
        finalError,
        status,
        taskId,
        deviceId
      ]);

      console.log(`📊 [传输状态更新] 数据库更新结果:`, {
        affectedRows: updateResult.affectedRows,
        taskId,
        deviceId,
        status,
        progressPercentage
      });

      if (updateResult.affectedRows === 0) {
        console.log(`⚠️ [传输状态更新] 没有找到匹配的传输记录:`, { taskId, deviceId });
        return res.status(404).json({
          success: false,
          message: '传输记录不存在'
        });
      }

      // 通知前端状态更新
      io.emit('xiaohongshu_transfer_status_update', {
        taskId,
        deviceId,
        status,
        message: finalMessage,
        error: finalError,
        progressPercentage,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ [传输状态更新] 状态更新成功:`, { taskId, deviceId, status, progressPercentage });

      res.json({
        success: true,
        message: '传输状态已更新',
        data: {
          taskId,
          deviceId,
          status,
          progressPercentage
        }
      });

    } catch (error) {
      console.error('❌ [传输状态更新] 更新传输状态失败:', error);
      res.status(500).json({
        success: false,
        message: '更新失败: ' + error.message
      });
    }
  });

  // 小红书实时状态API（统一处理所有功能）(原始文件第7699行)
  app.post('/api/xiaohongshu/realtime-status', async (req, res) => {
    try {
      const { deviceId, functionType, status, progress, message, stage, data } = req.body;

      if (!deviceId || !functionType) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 构造实时状态数据
      const statusData = {
        deviceId,
        functionType,
        status,
        progress: progress || 0,
        message: message || '',
        stage: stage || 'unknown',
        data: data || {},
        timestamp: new Date().toISOString()
      };

      // 根据功能类型处理特定逻辑
      switch (functionType) {
        case 'profile':
          // 修改资料功能的状态处理
          if (stage === 'updating_profile') {
            statusData.message = '正在修改个人资料...';
          } else if (stage === 'profile_updated') {
            statusData.message = '个人资料修改完成';
            statusData.progress = 100;
          }
          break;

        case 'groupChat':
          // 群聊功能的状态处理
          if (stage === 'searching_groups') {
            statusData.message = '正在搜索群聊...';
          } else if (stage === 'joining_group') {
            statusData.message = '正在加入群聊...';
          } else if (stage === 'sending_message') {
            statusData.message = '正在发送消息...';
          }
          break;

        case 'uidMessage':
          // UID私信功能的状态处理
          if (stage === 'searching_user') {
            statusData.message = `正在搜索用户: ${data.currentUid || ''}`;
          } else if (stage === 'sending_message') {
            statusData.message = `正在发送私信: ${data.currentUid || ''}`;
          } else if (stage === 'message_sent') {
            statusData.message = `私信发送成功: ${data.currentUid || ''}`;
          }
          break;

        case 'videoPublish':
          // 视频发布功能的状态处理
          if (stage === 'uploading_video') {
            statusData.message = `正在上传视频: ${data.currentVideo || ''}`;
          } else if (stage === 'setting_title') {
            statusData.message = '正在设置标题和描述...';
          } else if (stage === 'publishing') {
            statusData.message = '正在发布视频...';
          }
          break;

        default:
          // 默认处理
          break;
      }

      // 通知所有Web客户端实时状态更新
      io.emit('xiaohongshu_realtime_status', statusData);

      // 如果有日志服务，更新执行日志
      if (xiaohongshuLogService && data.taskId) {
        try {
          let executionStatus = 'running';
          if (status === 'completed' || stage === 'completed') {
            executionStatus = 'completed';
          } else if (status === 'failed' || stage === 'error') {
            executionStatus = 'failed';
          } else if (status === 'stopped' || stage === 'stopped') {
            executionStatus = 'stopped';
          }

          await xiaohongshuLogService.updateExecutionStatus(
            data.taskId,
            executionStatus,
            progress || 0,
            stage || '执行中',
            message || ''
          );
        } catch (logError) {
          console.error('更新执行日志失败:', logError);
        }
      }

      console.log(`实时状态更新: ${deviceId} - ${functionType} - ${stage} (${progress}%)`);

      res.json({
        success: true,
        message: '实时状态已接收'
      });

    } catch (error) {
      console.error('处理实时状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 智能视频选择API (原始文件第7796行) - 已添加用户隔离
  app.post('/api/xiaohongshu/smart-select-videos', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceIds, videoCount } = req.body;
      const userId = req.currentUserId;

      console.log(`[智能视频选择] 用户${userId}请求智能选择视频，设备数: ${deviceIds?.length}, 视频数: ${videoCount}`);

      if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID列表'
        });
      }

      const requestedVideoCount = videoCount || deviceIds.length;

      if (!pool) {
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      const connection = await pool.getConnection();

      try {
        // 获取所有可用视频，包含传输信息（按用户过滤）
        const [allVideos] = await connection.execute(
          `SELECT
            vf.id,
            vf.original_name,
            vf.file_size,
            vf.transfer_count,
            vf.transferred_devices,
            vf.last_transfer_time,
            COUNT(DISTINCT vt.device_id) as total_transfers,
            COUNT(CASE WHEN vt.status = 'completed' THEN 1 END) as successful_transfers
          FROM xiaohongshu_video_files vf
          LEFT JOIN xiaohongshu_video_transfers vt ON vf.id = vt.video_id AND vt.user_id = ?
          WHERE vf.status = 'active' AND vf.user_id = ?
          GROUP BY vf.id
          ORDER BY vf.upload_time DESC`,
          [userId, userId]
        );

        if (allVideos.length === 0) {
          connection.release();
          return res.status(400).json({
            success: false,
            message: '没有可用的视频文件'
          });
        }

        // 智能选择视频的逻辑（与原始server.js完全一致）
        const selectedVideos = smartSelectVideos(allVideos, deviceIds, requestedVideoCount);

        connection.release();

        res.json({
          success: true,
          message: `智能选择了 ${selectedVideos.length} 个视频`,
          data: {
            selectedVideos: selectedVideos,
            totalAvailable: allVideos.length,
            selectionStrategy: getSelectionStrategy(allVideos, deviceIds)
          }
        });

      } catch (dbError) {
        connection.release();
        throw dbError;
      }

    } catch (error) {
      console.error('智能视频选择失败:', error);
      res.status(500).json({
        success: false,
        message: '智能视频选择失败: ' + error.message
      });
    }
  });

  // 智能视频选择算法（与原始server.js完全一致）
  function smartSelectVideos(allVideos, deviceIds, requestedCount) {
    const selectedVideos = [];
    const deviceIdsSet = new Set(deviceIds);

    // 1. 优先选择从未传输过的视频
    const neverTransferred = allVideos.filter(video =>
      (video.total_transfers || 0) === 0
    );

    // 2. 选择没有传输到当前设备的视频
    const notTransferredToSelectedDevices = allVideos.filter(video => {
      if (!video.transferred_devices) return true;

      try {
        const transferredDevices = typeof video.transferred_devices === 'string'
          ? JSON.parse(video.transferred_devices)
          : video.transferred_devices;

        if (!Array.isArray(transferredDevices)) return true;

        // 检查是否有任何选中的设备已经传输过这个视频
        return !transferredDevices.some(device =>
          deviceIdsSet.has(device.deviceId)
        );
      } catch (e) {
        return true; // 解析失败时认为没有传输过
      }
    });

    // 3. 按传输次数排序（传输次数少的优先）
    const sortedByTransferCount = [...allVideos].sort((a, b) => {
      const aCount = a.total_transfers || 0;
      const bCount = b.total_transfers || 0;
      return aCount - bCount;
    });

    // 选择策略：优先级递减
    const selectionPools = [
      neverTransferred,
      notTransferredToSelectedDevices,
      sortedByTransferCount
    ];

    for (const pool of selectionPools) {
      if (selectedVideos.length >= requestedCount) break;

      const remainingCount = requestedCount - selectedVideos.length;
      const availableVideos = pool.filter(video =>
        !selectedVideos.some(selected => selected.id === video.id)
      );

      // 随机选择或按顺序选择
      const videosToAdd = availableVideos.slice(0, remainingCount);
      selectedVideos.push(...videosToAdd);
    }

    return selectedVideos;
  }

  // 获取选择策略说明（与原始server.js完全一致）
  function getSelectionStrategy(allVideos, deviceIds) {
    const neverTransferredCount = allVideos.filter(v => (v.total_transfers || 0) === 0).length;
    const deviceIdsSet = new Set(deviceIds);

    const notToSelectedDevicesCount = allVideos.filter(video => {
      if (!video.transferred_devices) return true;
      try {
        const transferredDevices = typeof video.transferred_devices === 'string'
          ? JSON.parse(video.transferred_devices)
          : video.transferred_devices;
        if (!Array.isArray(transferredDevices)) return true;
        return !transferredDevices.some(device => deviceIdsSet.has(device.deviceId));
      } catch (e) {
        return true;
      }
    }).length;

    return {
      totalVideos: allVideos.length,
      neverTransferredCount: neverTransferredCount,
      notToSelectedDevicesCount: notToSelectedDevicesCount,
      strategy: 'Priority: Never transferred > Not to selected devices > Least transferred'
    };
  }

  // 获取已上传的视频文件列表API (原始文件第7961行) - 已添加用户隔离
  app.get('/api/xiaohongshu/video-files', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { page = 1, limit = 20, status = 'active' } = req.query;
      const userId = req.currentUserId;
      const offset = (page - 1) * limit;

      console.log(`[视频文件列表] 用户${userId}查询视频文件列表: page=${page}, limit=${limit}, status=${status}`);

      const connection = await pool.getConnection();

      try {
        // 获取视频文件列表（包含传输信息，带用户过滤）
        let videos = [];

        try {
          // 首先检查传输相关表是否存在
          const [transferTables] = await connection.execute(`
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = 'autojs_control'
            AND TABLE_NAME IN ('xiaohongshu_video_transfers', 'xiaohongshu_video_assignments')
          `);

          if (transferTables.length > 0) {
            // 如果传输表存在，获取包含传输信息的视频列表（带用户过滤）
            const [videoResults] = await connection.execute(`
              SELECT
                vf.*,
                COUNT(DISTINCT vt.id) as transfer_count,
                COUNT(DISTINCT CASE WHEN vt.status = 'completed' THEN vt.id END) as completed_transfers,
                COUNT(DISTINCT vt.device_id) as transferred_to_devices,
                MAX(vt.transfer_time) as last_transfer_time
              FROM xiaohongshu_video_files vf
              LEFT JOIN xiaohongshu_video_transfers vt ON vf.id = vt.video_id AND vt.user_id = ?
              WHERE vf.status = ? AND vf.user_id = ?
              GROUP BY vf.id
              ORDER BY vf.upload_time DESC
              LIMIT ? OFFSET ?
            `, [userId, status, userId, parseInt(limit), parseInt(offset)]);

            videos = videoResults;
          } else {
            // 如果传输表不存在，只获取基本视频信息（带用户过滤）
            const [basicVideos] = await connection.execute(
              `SELECT * FROM xiaohongshu_video_files WHERE status = ? AND user_id = ? ORDER BY upload_time DESC LIMIT ? OFFSET ?`,
              [status, userId, parseInt(limit), parseInt(offset)]
            );
            videos = basicVideos;
          }
        } catch (error) {
          console.log('查询视频传输信息失败，使用基本信息:', error.message);
          // 查询失败，只获取基本视频信息
          const [basicVideos] = await connection.execute(
            `SELECT * FROM xiaohongshu_video_files WHERE status = ? ORDER BY upload_time DESC LIMIT ? OFFSET ?`,
            [status, parseInt(limit), parseInt(offset)]
          );

          // 直接返回基本视频信息
          videos = basicVideos;
        }

        // 获取总数
        const [countResult] = await connection.execute(
          'SELECT COUNT(*) as total FROM xiaohongshu_video_files WHERE status = ?',
          [status]
        );

        connection.release();

        res.json({
          success: true,
          data: {
            videos: videos,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: countResult[0].total,
              totalPages: Math.ceil(countResult[0].total / limit)
            }
          }
        });

      } catch (dbError) {
        connection.release();
        throw dbError;
      }

    } catch (error) {
      console.error('获取视频文件列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取视频文件列表失败: ' + error.message
      });
    }
  });

  // 获取视频文件统计信息API (原始文件第8155行) - 已添加用户隔离
  app.get('/api/xiaohongshu/video-stats', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;

      if (!pool) {
        return res.json({
          success: true,
          data: {
            totalVideos: 0,
            totalSize: 0,
            todayUploads: 0
          }
        });
      }

      console.log(`[视频统计] 用户${userId}查询视频统计信息`);

      const connection = await pool.getConnection();

      try {
        // 获取总数和总大小（按用户过滤）
        const [statsResult] = await connection.execute(
          `SELECT
            COUNT(*) as total_count,
            COALESCE(SUM(file_size), 0) as total_size
          FROM xiaohongshu_video_files
          WHERE status = 'active' AND user_id = ?`,
          [userId]
        );

        // 获取今日上传数量（按用户过滤）
        const [todayResult] = await connection.execute(
          `SELECT COUNT(*) as today_uploads
          FROM xiaohongshu_video_files
          WHERE status = 'active' AND user_id = ?
          AND DATE(upload_time) = CURDATE()`,
          [userId]
        );

        connection.release();

        const statsData = {
          totalVideos: statsResult[0].total_count,
          totalSize: statsResult[0].total_size,
          todayUploads: todayResult[0].today_uploads
        };

        console.log(`[视频统计] 用户${userId}统计结果:`, statsData);

        res.json({
          success: true,
          data: statsData
        });

      } catch (dbError) {
        connection.release();
        throw dbError;
      }

    } catch (error) {
      console.error('获取视频统计信息失败:', error);
      res.status(500).json({
        success: false,
        message: '获取视频统计信息失败: ' + error.message
      });
    }
  });

  // 删除视频文件API (原始文件第8213行)
  app.delete('/api/xiaohongshu/video-files/:videoId', async (req, res) => {
    try {
      const { videoId } = req.params;
      console.log(`🗑️ 开始删除视频文件: ${videoId}`);

      if (!pool) {
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      const connection = await pool.getConnection();

      try {
        // 获取视频文件信息
        const [videos] = await connection.execute(
          'SELECT file_path, thumbnail_path, original_name FROM xiaohongshu_video_files WHERE id = ? AND status = "active"',
          [videoId]
        );

        if (videos.length === 0) {
          connection.release();
          return res.status(404).json({
            success: false,
            message: '视频文件不存在'
          });
        }

        const video = videos[0];
        console.log(`🗑️ 找到视频文件: ${video.original_name}`);

        // 软删除视频文件记录
        await connection.execute(
          'UPDATE xiaohongshu_video_files SET status = "deleted" WHERE id = ?',
          [videoId]
        );

        console.log(`🗑️ 数据库记录已标记为删除: ${videoId}`);

        // 删除物理视频文件
        if (video.file_path && fs.existsSync(video.file_path)) {
          try {
            fs.unlinkSync(video.file_path);
            console.log(`🗑️ 视频文件已删除: ${video.file_path}`);
          } catch (fileError) {
            console.warn('删除视频文件失败:', fileError.message);
          }
        }

        // 删除缩略图文件
        if (video.thumbnail_path) {
          try {
            // 处理相对路径和绝对路径
            let thumbnailFullPath = video.thumbnail_path;
            if (video.thumbnail_path.startsWith('/thumbnails/')) {
              thumbnailFullPath = path.join(__dirname, '../uploads/thumbnails', path.basename(video.thumbnail_path));
            } else if (video.thumbnail_path.startsWith('/uploads/')) {
              thumbnailFullPath = path.join(__dirname, '..', video.thumbnail_path);
            }

            if (fs.existsSync(thumbnailFullPath)) {
              fs.unlinkSync(thumbnailFullPath);
              console.log(`🗑️ 缩略图文件已删除: ${thumbnailFullPath}`);
            }
          } catch (thumbError) {
            console.warn('删除缩略图文件失败:', thumbError.message);
          }
        }

        connection.release();

        res.json({
          success: true,
          message: '视频文件删除成功'
        });

      } catch (dbError) {
        connection.release();
        throw dbError;
      }

    } catch (error) {
      console.error('删除视频文件失败:', error);
      res.status(500).json({
        success: false,
        message: '删除视频文件失败: ' + error.message
      });
    }
  });

  // 分配视频给设备API (原始文件第8175行)
  app.post('/api/xiaohongshu/assign-videos', async (req, res) => {
    try {
      console.log(`📋 [视频分配API] 收到请求，请求体:`, JSON.stringify(req.body, null, 2));

      const { deviceId, deviceIds, videoIds, assignmentMode = 'manual', strategy, taskId } = req.body;

      // 兼容前端发送的参数格式：支持deviceId(单数)或deviceIds(复数)
      const finalDeviceId = deviceId || (deviceIds && deviceIds[0]);

      console.log(`📋 [视频分配API] 解析参数:`, {
        deviceId,
        deviceIds,
        finalDeviceId,
        videoIds,
        videoIdsType: typeof videoIds,
        videoIdsIsArray: Array.isArray(videoIds),
        assignmentMode,
        strategy,
        taskId
      });

      if (!finalDeviceId || !videoIds || !Array.isArray(videoIds)) {
        console.log(`❌ [视频分配API] 参数验证失败:`, {
          hasFinalDeviceId: !!finalDeviceId,
          hasVideoIds: !!videoIds,
          videoIdsIsArray: Array.isArray(videoIds)
        });
        return res.status(400).json({
          success: false,
          message: '缺少必要参数: deviceId(或deviceIds)和videoIds(数组)是必需的',
          details: {
            deviceId: !!deviceId,
            deviceIds: !!deviceIds,
            finalDeviceId: !!finalDeviceId,
            videoIds: !!videoIds,
            videoIdsIsArray: Array.isArray(videoIds)
          }
        });
      }

      console.log(`📋 [视频分配API] 分配视频给设备: ${finalDeviceId}, 视频数量: ${videoIds.length}`);

      if (!pool) {
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      const connection = await pool.getConnection();
      const assignmentResults = [];
      let successCount = 0;
      let failCount = 0;

      try {
        await connection.beginTransaction();

        for (const videoId of videoIds) {
          try {
            // 从数据库查询视频信息
            const [videos] = await connection.execute(
              'SELECT id, original_name, status FROM xiaohongshu_video_files WHERE id = ? AND status = "active"',
              [videoId]
            );

            if (videos.length === 0) {
              assignmentResults.push({
                videoId,
                success: false,
                message: '视频不存在或已删除'
              });
              failCount++;
              continue;
            }

            const video = videos[0];

            // 检查是否已经分配给该设备（如果有分配表的话）
            try {
              const [existingAssignments] = await connection.execute(`
                SELECT id FROM xiaohongshu_video_assignments
                WHERE video_id = ? AND device_id = ? AND status = 'active'
              `, [videoId, finalDeviceId]);

              if (existingAssignments.length > 0) {
                assignmentResults.push({
                  videoId,
                  videoName: video.original_name,
                  success: false,
                  message: '视频已分配给该设备'
                });
                failCount++;
                continue;
              }
            } catch (assignmentCheckError) {
              // 如果分配表不存在，继续处理
              console.log('分配表不存在，跳过重复检查');
            }

            // 创建视频分配记录（如果表存在）
            try {
              await connection.execute(`
                INSERT INTO xiaohongshu_video_assignments
                (video_id, device_id, assignment_mode, assigned_time, status)
                VALUES (?, ?, ?, NOW(), 'active')
              `, [videoId, finalDeviceId, assignmentMode]);
            } catch (assignmentInsertError) {
              // 如果分配表不存在，只记录日志
              console.log('分配表不存在，跳过分配记录创建');
            }

            assignmentResults.push({
              videoId,
              videoName: video.original_name,
              success: true,
              message: '分配成功'
            });
            successCount++;

            console.log(`视频已分配: ${videoId} -> ${deviceId}`);

          } catch (videoError) {
            console.error(`分配视频 ${videoId} 失败:`, videoError);
            assignmentResults.push({
              videoId,
              success: false,
              message: '分配失败: ' + videoError.message
            });
            failCount++;
          }
        }

        await connection.commit();
        connection.release();

      } catch (transactionError) {
        await connection.rollback();
        connection.release();
        throw transactionError;
      }

      res.json({
        success: true,
        message: `视频分配完成，成功: ${successCount}个，失败: ${failCount}个`,
        data: {
          deviceId: finalDeviceId,
          assignmentMode,
          successCount,
          failCount,
          totalCount: videoIds.length,
          results: assignmentResults
        }
      });

    } catch (error) {
      console.error('分配视频失败:', error);
      res.status(500).json({
        success: false,
        message: '分配失败: ' + error.message
      });
    }
  });

  // 获取设备的视频分配情况API (原始文件第8325行)
  app.get('/api/xiaohongshu/device-video-assignments/:deviceId', async (req, res) => {
    try {
      const { deviceId } = req.params;
      const { status } = req.query;

      console.log(`查询设备视频分配: ${deviceId}`);

      if (!pool) {
        return res.json({
          success: true,
          data: {
            deviceId,
            stats: { totalAssigned: 0, uploaded: 0, assigned: 0, publishing: 0, published: 0, failed: 0, totalSize: 0 },
            videos: []
          }
        });
      }

      const connection = await pool.getConnection();

      try {
        let assignedVideos = [];

        // 尝试从分配表查询
        try {
          const [assignments] = await connection.execute(`
            SELECT
              va.video_id,
              va.assigned_time,
              va.status as assignment_status,
              vf.original_name,
              vf.file_size,
              vf.video_format,
              vf.status as video_status
            FROM xiaohongshu_video_assignments va
            LEFT JOIN xiaohongshu_video_files vf ON va.video_id = vf.id
            WHERE va.device_id = ? AND va.status = 'active'
            ORDER BY va.assigned_time DESC
          `, [deviceId]);

          assignedVideos = assignments.map(assignment => ({
            id: assignment.video_id,
            originalName: assignment.original_name,
            size: assignment.file_size || 0,
            status: assignment.assignment_status,
            assignedAt: assignment.assigned_time,
            videoStatus: assignment.video_status,
            publishResults: []
          }));

        } catch (assignmentError) {
          console.log('分配表不存在，返回空结果');
          assignedVideos = [];
        }

        // 过滤状态
        let filteredVideos = assignedVideos;
        if (status) {
          filteredVideos = assignedVideos.filter(video => video.status === status);
        }

        // 统计信息
        const stats = {
          totalAssigned: assignedVideos.length,
          uploaded: assignedVideos.filter(v => v.status === 'uploaded').length,
          assigned: assignedVideos.filter(v => v.status === 'assigned').length,
          publishing: assignedVideos.filter(v => v.status === 'publishing').length,
          published: assignedVideos.filter(v => v.status === 'published').length,
          failed: assignedVideos.filter(v => v.status === 'failed').length,
          totalSize: assignedVideos.reduce((sum, video) => sum + video.size, 0)
        };

        connection.release();

        res.json({
          success: true,
          data: {
            deviceId,
            stats,
            videos: filteredVideos
          }
        });

      } catch (dbError) {
        connection.release();
        throw dbError;
      }

    } catch (error) {
      console.error('查询设备视频分配失败:', error);
      res.status(500).json({
        success: false,
        message: '查询失败: ' + error.message
      });
    }
  });

  // 视频发布进度上报API (原始文件第8385行) - 改为设备认证模式
  app.post('/api/xiaohongshu/video-publish-progress', authenticateDevice(pool), async (req, res) => {
    try {
      const { deviceId, videoId, progress, stage, message, timestamp } = req.body;
      const { userId, username } = req.device; // 来自设备认证中间件

      if (!deviceId || !videoId) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      console.log(`📊 [视频发布进度] 用户${userId}设备${deviceId}: 视频=${videoId}, 进度=${progress}%, 阶段=${stage}`);

      const video = videoStorage.get(videoId);
      if (!video) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      // 更新视频状态
      if (stage === 'uploading' || stage === 'processing') {
        video.status = 'publishing';
      }

      // 记录发布进度
      if (!video.publishProgress) {
        video.publishProgress = {};
      }

      video.publishProgress[deviceId] = {
        progress: progress || 0,
        stage: stage || 'unknown',
        message: message || '',
        timestamp: timestamp || new Date(),
        lastUpdated: new Date()
      };

      // 只向设备所属用户的客户端通知进度更新
      const progressData = {
        deviceId,
        videoId,
        videoName: video.originalName,
        progress,
        stage,
        message,
        timestamp: timestamp || new Date().toISOString()
      };

      broadcastToUserClients(userId, 'xiaohongshu_video_publish_progress', progressData);
      console.log(`✅ [视频发布进度] 已广播给用户${userId}的客户端`);

      res.json({
        success: true,
        message: '发布进度已更新'
      });

    } catch (error) {
      console.error('上报视频发布进度失败:', error);
      res.status(500).json({
        success: false,
        message: '上报失败: ' + error.message
      });
    }
  });

  // 视频发布结果上报API (原始文件第8455行) - 改为设备认证模式
  app.post('/api/xiaohongshu/video-publish-result', authenticateDevice(pool), async (req, res) => {
    try {
      const { deviceId, videoId, success, message, publishUrl, publishTime, error } = req.body;
      const { userId, username } = req.device; // 来自设备认证中间件

      if (!deviceId || !videoId) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      console.log(`📊 [视频发布结果] 用户${userId}设备${deviceId}: 视频=${videoId}, 成功=${success}`);

      const video = videoStorage.get(videoId);
      if (!video) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      // 更新视频状态
      video.status = success ? 'published' : 'failed';

      // 记录发布结果
      if (!video.publishResults) {
        video.publishResults = [];
      }

      const publishResult = {
        deviceId,
        success,
        message: message || '',
        publishUrl: publishUrl || null,
        publishTime: publishTime || new Date(),
        error: error || null,
        timestamp: new Date()
      };

      video.publishResults.push(publishResult);

      // 只向设备所属用户的客户端通知发布结果
      const resultData = {
        deviceId,
        videoId,
        videoName: video.originalName,
        success,
        message,
        publishUrl,
        publishTime,
        error,
        timestamp: new Date().toISOString()
      };

      broadcastToUserClients(userId, 'xiaohongshu_video_publish_result', resultData);
      console.log(`✅ [视频发布结果] 用户${userId}设备${deviceId}: ${videoId} - ${success ? '成功' : '失败'}, 已广播给用户客户端`);

      res.json({
        success: true,
        message: '发布结果已记录'
      });

    } catch (error) {
      console.error('上报视频发布结果失败:', error);
      res.status(500).json({
        success: false,
        message: '上报失败: ' + error.message
      });
    }
  });

  // 停止设备任务API (原始文件第8525行)
  app.post('/api/xiaohongshu/stop-device-tasks', authenticateToken, async (req, res) => {
    try {
      const { deviceId, taskTypes = [], reason } = req.body;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      console.log(`停止设备任务: ${deviceId}, 任务类型: ${taskTypes.join(', ')}`);

      // 查找设备的活跃任务
      let targetTasks = Array.from(xiaohongshuActiveTasks.values()).filter(task =>
        task.deviceIds.includes(deviceId) && task.status === 'running'
      );

      // 如果指定了任务类型，进一步过滤
      if (taskTypes.length > 0) {
        targetTasks = targetTasks.filter(task => taskTypes.includes(task.functionType));
      }

      let stoppedCount = 0;
      const results = [];

      for (const task of targetTasks) {
        try {
          // 查找设备
          let targetDevice = null;
          let targetSocket = null;

          for (const [socketId, deviceData] of devices) {
            if (deviceData.deviceId === deviceId) {
              targetDevice = deviceData;
              targetSocket = io.sockets.sockets.get(socketId);
              break;
            }
          }

          if (targetDevice && targetSocket) {
            // 发送停止命令
            targetSocket.emit('stop_script', {
              taskId: task.id,
              functionType: task.functionType,
              reason: reason || '管理员停止设备任务'
            });

            // 更新任务状态
            const deviceResult = task.results.find(r => r.deviceId === deviceId);
            if (deviceResult) {
              deviceResult.status = 'stopped';
              deviceResult.message = reason || '管理员停止设备任务';
            }

            stoppedCount++;
            results.push({
              taskId: task.id,
              functionType: task.functionType,
              status: 'stopped',
              message: '停止命令已发送'
            });

            console.log(`已停止设备任务: ${deviceId} - ${task.functionType} - ${task.id}`);
          } else {
            results.push({
              taskId: task.id,
              functionType: task.functionType,
              status: 'offline',
              message: '设备离线，无法发送停止命令'
            });
          }

        } catch (error) {
          console.error(`停止设备任务失败: ${task.id}`, error);
          results.push({
            taskId: task.id,
            functionType: task.functionType,
            status: 'error',
            message: error.message
          });
        }
      }

      res.json({
        success: true,
        message: `设备任务停止完成，成功停止 ${stoppedCount} 个任务`,
        data: {
          deviceId,
          taskTypes,
          stoppedCount,
          totalTasks: targetTasks.length,
          results
        }
      });

    } catch (error) {
      console.error('停止设备任务失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 测试API端点（用于CORS测试）
  app.get('/api/xiaohongshu/test', (req, res) => {
    console.log('🧪 [测试API] 收到测试请求');
    console.log('🧪 [测试API] Origin:', req.headers.origin);
    console.log('🧪 [测试API] User-Agent:', req.headers['user-agent']);
    console.log('🧪 [测试API] Method:', req.method);
    console.log('🧪 [测试API] URL:', req.url);

    res.json({
      success: true,
      message: '小红书模块CORS测试成功',
      timestamp: new Date().toISOString(),
      origin: req.headers.origin || 'no-origin',
      userAgent: req.headers['user-agent'] || 'no-user-agent',
      method: req.method,
      url: req.url,
      module: 'xiaohongshu'
    });
  });

  // 测试POST API端点（用于CORS预检请求测试）
  app.post('/api/xiaohongshu/test-post', (req, res) => {
    console.log('🧪 [测试POST API] 收到POST测试请求');
    console.log('🧪 [测试POST API] Origin:', req.headers.origin);
    console.log('🧪 [测试POST API] Body:', req.body);

    res.json({
      success: true,
      message: '小红书模块POST CORS测试成功',
      timestamp: new Date().toISOString(),
      origin: req.headers.origin || 'no-origin',
      receivedData: req.body,
      module: 'xiaohongshu'
    });
  });

  console.log('✅ 小红书自动化模块设置完成');

  // 返回小红书自动化相关函数供其他模块使用
  return {
    xiaohongshuActiveTasks,
    xiaohongshuTaskHistory,
    uidStorage,
    uidMessageStorage,
    videoStorage,
    videoTransferRecords
  };
}

// 计算文件MD5哈希值
function calculateFileHash(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('md5');
    const stream = fs.createReadStream(filePath);

    stream.on('data', data => hash.update(data));
    stream.on('end', () => resolve(hash.digest('hex')));
    stream.on('error', reject);
  });
}

// 使用ffmpeg获取视频信息（如果可用）
function getVideoInfo(filePath) {
  return new Promise((resolve, reject) => {
    // 模拟视频信息（用于演示）
    const fileStats = fs.statSync(filePath);
    const fileExt = path.extname(filePath).toLowerCase();

    // 根据文件扩展名和大小模拟视频信息
    let mockDuration = Math.floor(Math.random() * 300) + 30; // 30-330秒
    let mockWidth = 1920;
    let mockHeight = 1080;

    // 根据文件大小调整模拟参数
    if (fileStats.size < 10 * 1024 * 1024) { // 小于10MB
      mockWidth = 1280;
      mockHeight = 720;
      mockDuration = Math.floor(Math.random() * 120) + 15;
    } else if (fileStats.size > 100 * 1024 * 1024) { // 大于100MB
      mockWidth = 3840;
      mockHeight = 2160;
      mockDuration = Math.floor(Math.random() * 600) + 120;
    }

    const videoInfo = {
      duration: mockDuration,
      resolution: `${mockWidth}x${mockHeight}`,
      width: mockWidth,
      height: mockHeight,
      format: fileExt.substring(1),
      size: fileStats.size
    };

    console.log(`模拟视频信息: ${path.basename(filePath)}`, videoInfo);
    resolve(videoInfo);
  });
}

// 生成视频缩略图（模拟实现）
function generateThumbnail(videoPath, fileName, timeOffset = '00:00:01') {
  return new Promise((resolve, reject) => {
    console.log(`生成缩略图 - 输入: ${videoPath}, 文件名: ${fileName}`);

    // 创建缩略图目录
    const thumbnailDir = path.join(__dirname, '../uploads/thumbnails');
    if (!fs.existsSync(thumbnailDir)) {
      fs.mkdirSync(thumbnailDir, { recursive: true });
    }

    // 生成缩略图文件名（使用PNG格式）
    const thumbnailFileName = `thumb_${fileName.replace(/\.[^/.]+$/, '')}.png`;
    const outputPath = path.join(thumbnailDir, thumbnailFileName);

    // 由于没有ffmpeg，我们创建一个简单的占位符图片
    // 这里我们返回一个相对URL路径，前端可以显示默认图标
    const relativePath = `/thumbnails/${thumbnailFileName}`;

    // 创建一个简单的SVG缩略图作为占位符（但保存为SVG格式供前端使用）
    const svgFileName = `thumb_${fileName.replace(/\.[^/.]+$/, '')}.svg`;
    const svgOutputPath = path.join(thumbnailDir, svgFileName);
    const svgContent = `<svg width="320" height="240" xmlns="http://www.w3.org/2000/svg">
  <rect width="320" height="240" fill="#f0f0f0"/>
  <rect x="10" y="10" width="300" height="220" fill="#e0e0e0" stroke="#ccc" stroke-width="2"/>
  <circle cx="160" cy="120" r="30" fill="#409eff"/>
  <polygon points="150,105 150,135 175,120" fill="white"/>
  <text x="160" y="180" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
    ${path.basename(videoPath, path.extname(videoPath))}
  </text>
  <text x="160" y="200" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">
    视频预览
  </text>
</svg>`;

    try {
      fs.writeFileSync(svgOutputPath, svgContent);
      console.log(`默认缩略图已生成: ${svgOutputPath}`);

      // 返回SVG文件的相对路径
      const svgRelativePath = `/thumbnails/${svgFileName}`;
      resolve(svgRelativePath);
    } catch (error) {
      console.error('生成默认缩略图失败:', error);
      // 即使生成失败，也返回null而不是抛出错误
      resolve(null);
    }
  });
}

module.exports = { setupServerXiaohongshu };